#!/bin/bash

# 🚀 直接部署到GitHub Pages
# 用途：直接将web文件夹部署到gh-pages分支

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}💡 $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示标题
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════╗"
echo "║            🚀 直接部署到GitHub Pages (gh-pages)         ║"
echo "╚══════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查web文件夹
if [ ! -d "web" ]; then
    print_error "web文件夹不存在！"
    exit 1
fi

print_info "准备部署web文件夹到GitHub Pages..."

# 创建临时目录
TEMP_DIR=$(mktemp -d)
cp -r web/* "$TEMP_DIR/"

# 切换到gh-pages分支
if git show-ref --verify --quiet refs/heads/gh-pages; then
    print_info "切换到现有的gh-pages分支..."
    git checkout gh-pages
    # 清空当前内容
    git rm -rf . 2>/dev/null || true
    # 保留.git目录
    find . -name ".git" -prune -o -type f -exec rm -f {} \;
else
    print_info "创建新的gh-pages分支..."
    git checkout --orphan gh-pages
    git rm -rf . 2>/dev/null || true
fi

# 复制web文件夹内容
cp -r "$TEMP_DIR"/* .

# 清理临时目录
rm -rf "$TEMP_DIR"

# 添加所有文件
git add .

# 提交到gh-pages
COMMIT_MSG="🌐 部署网站 $(date '+%Y-%m-%d %H:%M:%S')"
git commit -m "$COMMIT_MSG" || print_info "没有新的变化需要提交"

# 推送gh-pages分支
print_info "推送到gh-pages分支..."
git push -f origin gh-pages

print_success "网站已成功部署到GitHub Pages！"

echo ""
echo -e "${GREEN}🎉 部署完成！${NC}"
echo ""
print_info "🌐 你的网站地址："
echo "   主页: https://jerrypanguo.github.io/musicqrcode_verify/"
echo "   验证页面: https://jerrypanguo.github.io/musicqrcode_verify/simple_verify.html"
echo "   管理后台: https://jerrypanguo.github.io/musicqrcode_verify/admin.html"
echo ""
print_info "📝 说明："
echo "   - 网站更新可能需要2-5分钟才能生效"
echo "   - 如果网站没有立即更新，请等待几分钟"
echo "   - 可以在GitHub仓库的Settings > Pages中查看部署状态" 