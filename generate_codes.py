#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
书籍二维码生成器
功能：批量生成唯一的二维码并制作成可打印的PDF文件
"""

import os
import json
import uuid
import qrcode
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from typing import List, Dict
import secrets
import string
import subprocess
import sys
import re


class QRCodeGenerator:
    """二维码生成器类"""
    
    def __init__(self, base_url: str = "https://jerrypanguo.github.io/musicqrcode_verify"):
        """
        初始化二维码生成器
        
        Args:
            base_url: 验证页面的基础URL（需要替换为您的GitHub Pages地址）
        """
        self.base_url = base_url
        self.output_dir = "output"
        self.qrcode_dir = os.path.join(self.output_dir, "qrcodes")
        self.data_dir = "data"
        self.web_dir = "web"
        
        # 创建必要的目录
        os.makedirs(self.qrcode_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
    
    def generate_unique_code(self, length: int = 12) -> str:
        """
        生成唯一的验证码
        
        Args:
            length: 验证码长度
            
        Returns:
            str: 唯一验证码
        """
        # 使用字母和数字，排除容易混淆的字符
        alphabet = string.ascii_uppercase + string.digits
        alphabet = alphabet.replace('0', '').replace('O', '').replace('I', '').replace('1', '')
        
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def create_qrcode(self, code: str) -> str:
        """
        创建单个二维码
        
        Args:
            code: 验证码
            
        Returns:
            str: 二维码图片文件路径
        """
        # 构建验证URL - 直接指向根目录的验证页面
        verify_url = f"{self.base_url}/simple_verify.html?code={code}"
        
        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(verify_url)
        qr.make(fit=True)
        
        # 生成二维码图片
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # 保存图片
        img_path = os.path.join(self.qrcode_dir, f"qr_{code}.png")
        qr_img.save(img_path)
        
        return img_path
    
    def generate_codes_data(self, count: int) -> List[Dict]:
        """
        生成指定数量的验证码数据
        
        Args:
            count: 生成数量
            
        Returns:
            List[Dict]: 验证码数据列表
        """
        codes_data = []
        existing_codes = set()
        
        # 读取现有验证码以避免重复
        codes_file = os.path.join(self.data_dir, "codes.json")
        if os.path.exists(codes_file):
            try:
                with open(codes_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    existing_codes = {item['code'] for item in existing_data if isinstance(item, dict) and 'code' in item}
            except:
                pass
        
        for i in range(count):
            # 确保验证码唯一
            while True:
                code = self.generate_unique_code()
                if code not in existing_codes:
                    existing_codes.add(code)
                    break
            
            # 创建二维码图片
            img_path = self.create_qrcode(code)
            
            code_data = {
                "code": code,
                "created_date": datetime.now().isoformat(),
                "activated": False,
                "activation_date": None,
                "img_path": img_path
            }
            
            codes_data.append(code_data)
            print(f"已生成验证码 {i+1}/{count}: {code}")
        
        return codes_data
    
    def save_codes_data(self, codes_data: List[Dict]):
        """
        保存验证码数据到JSON文件
        
        Args:
            codes_data: 验证码数据列表
        """
        # 读取现有数据
        codes_file = os.path.join(self.data_dir, "codes.json")
        existing_codes = []
        
        if os.path.exists(codes_file):
            try:
                with open(codes_file, 'r', encoding='utf-8') as f:
                    existing_codes = json.load(f)
            except:
                existing_codes = []
        
        # 合并数据
        all_codes = existing_codes + codes_data
        
        # 保存数据
        with open(codes_file, 'w', encoding='utf-8') as f:
            json.dump(all_codes, f, ensure_ascii=False, indent=2)
        
        print(f"验证码数据已保存到: {codes_file}")
    
    def create_pdf(self, codes_data: List[Dict], orientation: str = "landscape"):
        """
        创建可打印的PDF文件
        
        Args:
            codes_data: 验证码数据列表
            orientation: 页面方向，"landscape"(横版) 或 "portrait"(竖版)
        """
        if orientation == "landscape":
            self._create_landscape_pdf(codes_data)
        elif orientation == "portrait":
            self._create_portrait_pdf(codes_data)
        else:
            raise ValueError("orientation must be 'landscape' or 'portrait'")
    
    def _create_landscape_pdf(self, codes_data: List[Dict]):
        """创建横版A4 PDF文件（原有功能）"""
        pdf_path = os.path.join(self.output_dir, "qrcode_sheet_横版.pdf")
        
        # 创建横版A4 PDF文档
        from reportlab.lib.pagesizes import landscape, A4
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=landscape(A4),  # 横版A4
            rightMargin=3*cm,
            leftMargin=3*cm,
            topMargin=3*cm,
            bottomMargin=3*cm
        )
        
        # 注册字体
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 尝试注册Bodoni 72字体作为标题字体
        try:
            pdfmetrics.registerFont(TTFont('Bodoni72', 'fonts/Bodoni 72.ttc'))
            title_font = 'Bodoni72'
        except:
            try:
                # 备选方案：使用Brush Script
                pdfmetrics.registerFont(TTFont('BrushScript', '/System/Library/Fonts/Supplemental/Brush Script.ttf'))
                title_font = 'BrushScript'
            except:
                try:
                    # 备选方案：使用系统的其他衬线字体
                    pdfmetrics.registerFont(TTFont('Baskerville', '/System/Library/Fonts/Baskerville.ttc'))
                    title_font = 'Baskerville'
                except:
                    title_font = 'Times-Roman'
        
        # 尝试注册衬线字体作为验证码字体（PTSerif保持不变）
        try:
            pdfmetrics.registerFont(TTFont('PTSerif', '/System/Library/Fonts/Supplemental/PTSerif.ttc'))
            code_font = 'PTSerif'
        except:
            try:
                # 备选方案：使用Monaco或其他等宽字体
                pdfmetrics.registerFont(TTFont('Monaco', '/System/Library/Fonts/Monaco.ttf'))
                code_font = 'Monaco'
            except:
                code_font = 'Courier-Bold'
        
        # 样式
        styles = getSampleStyleSheet()
        
        # 自定义标题样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Normal'],
            fontName=title_font,
            fontSize=24,
            spaceAfter=40,
            alignment=TA_CENTER,
            textColor=colors.black
        )
        
        # 自定义验证码样式
        code_style = ParagraphStyle(
            'CodeStyle',
            parent=styles['Normal'],
            fontName=code_font,
            fontSize=18,
            spaceBefore=20,  # 减少上方间距，让验证码往上移
            alignment=TA_CENTER,
            textColor=colors.black
        )
        
        story = []
        
        # 为每个验证码创建一页
        for i, code_data in enumerate(codes_data):
            if i > 0:
                # 添加分页符（除了第一页）
                from reportlab.platypus import PageBreak
                story.append(PageBreak())
            
            # 添加垂直间距，让内容在页面中心偏上
            story.append(Spacer(1, 2*cm))
            
            # 标题文字 - 使用Christmas Serif字体
            title_text = "SCAN TO VERIFY AUTHENTICITY."
            title = Paragraph(title_text, title_style)
            story.append(title)
            
            # 二维码图片
            img_path = code_data['img_path']
            if os.path.exists(img_path):
                from reportlab.platypus import Image
                # 较大的二维码尺寸
                qr_img = Image(img_path, width=6*cm, height=6*cm)
                # 居中对齐
                qr_img.hAlign = 'CENTER'
                story.append(qr_img)
            
            # 验证码文本 - 使用Voyage字体，位置向上移动
            code_text = Paragraph(code_data['code'], code_style)
            story.append(code_text)
        
        # 生成PDF
        doc.build(story)
        print(f"横版PDF文件已生成: {pdf_path}")
        print(f"📄 格式：横版A4，每页一个二维码，极简设计")
        print(f"🎨 标题字体：{title_font}，验证码字体：{code_font}")
        print(f"📝 包含 {len(codes_data)} 页")

    def _create_portrait_pdf(self, codes_data: List[Dict]):
        """创建竖版A4 PDF文件（新增功能，遵循黄金比例）"""
        pdf_path = os.path.join(self.output_dir, "qrcode_sheet_竖版.pdf")
        
        # 创建竖版A4 PDF文档
        doc = SimpleDocTemplate(
            pdf_path,
            pagesize=A4,  # 竖版A4: 21×29.7cm
            rightMargin=2.5*cm,
            leftMargin=2.5*cm,
            topMargin=2.5*cm,
            bottomMargin=2.5*cm
        )
        
        # 注册字体（与横版相同）
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 尝试注册Bodoni 72字体作为标题字体
        try:
            pdfmetrics.registerFont(TTFont('Bodoni72', 'fonts/Bodoni 72.ttc'))
            title_font = 'Bodoni72'
        except:
            try:
                # 备选方案：使用Brush Script
                pdfmetrics.registerFont(TTFont('BrushScript', '/System/Library/Fonts/Supplemental/Brush Script.ttf'))
                title_font = 'BrushScript'
            except:
                try:
                    # 备选方案：使用系统的其他衬线字体
                    pdfmetrics.registerFont(TTFont('Baskerville', '/System/Library/Fonts/Baskerville.ttc'))
                    title_font = 'Baskerville'
                except:
                    title_font = 'Times-Roman'
        
        # 尝试注册衬线字体作为验证码字体
        try:
            pdfmetrics.registerFont(TTFont('PTSerif', '/System/Library/Fonts/Supplemental/PTSerif.ttc'))
            code_font = 'PTSerif'
        except:
            try:
                # 备选方案：使用Monaco或其他等宽字体
                pdfmetrics.registerFont(TTFont('Monaco', '/System/Library/Fonts/Monaco.ttf'))
                code_font = 'Monaco'
            except:
                code_font = 'Courier-Bold'
        
        # 样式配置（竖版优化，遵循黄金比例）
        styles = getSampleStyleSheet()
        
        # 黄金比例常数
        GOLDEN_RATIO = 1.618
        
        # 自定义标题样式（竖版优化）
        title_style = ParagraphStyle(
            'PortraitTitle',
            parent=styles['Normal'],
            fontName=title_font,
            fontSize=20,  # 竖版略小的字体
            spaceAfter=30,  # 标题下方间距
            alignment=TA_CENTER,
            textColor=colors.black,
            leading=24  # 行高
        )
        
        # 自定义验证码样式（竖版优化）
        code_style = ParagraphStyle(
            'PortraitCode',
            parent=styles['Normal'],
            fontName=code_font,
            fontSize=16,  # 验证码字体稍小
            spaceBefore=15,  # 二维码下方间距 - 从25改为15，让验证码更靠近二维码
            alignment=TA_CENTER,
            textColor=colors.black,
            leading=20
        )
        
        story = []
        
        # 为每个验证码创建一页（竖版布局）
        for i, code_data in enumerate(codes_data):
            if i > 0:
                from reportlab.platypus import PageBreak
                story.append(PageBreak())
            
            # 竖版布局设计（遵循黄金比例）
            # 页面内容区域高度约24.7cm，按黄金比例分割：
            # 上部分（标题+二维码）：约15.3cm
            # 下部分（验证码）：约9.4cm
            
            # 上部空白（稍微增加，让内容整体下移）
            story.append(Spacer(1, 4*cm))  # 从3cm改为4cm，让整体内容下移
            
            # 标题文字
            title_text = "SCAN TO VERIFY AUTHENTICITY."
            title = Paragraph(title_text, title_style)
            story.append(title)
            
            # 标题与二维码之间的间距（按黄金比例）
            story.append(Spacer(1, 1.5*cm))
            
            # 二维码图片（竖版使用相对较大但不过分的尺寸）
            img_path = code_data['img_path']
            if os.path.exists(img_path):
                from reportlab.platypus import Image
                # 竖版二维码尺寸：7cm（比横版稍大一点，利用竖版空间）
                qr_size = 7*cm
                qr_img = Image(img_path, width=qr_size, height=qr_size)
                qr_img.hAlign = 'CENTER'
                story.append(qr_img)
            
            # 二维码与验证码之间的间距（缩短距离）
            story.append(Spacer(1, 1*cm))  # 从2cm改为1cm，让验证码更靠近二维码
            
            # 验证码文本
            code_text = Paragraph(code_data['code'], code_style)
            story.append(code_text)
            
            # 底部留白空间（让整体更平衡）
            story.append(Spacer(1, 1*cm))
        
        # 生成PDF
        doc.build(story)
        print(f"竖版PDF文件已生成: {pdf_path}")
        print(f"📄 格式：竖版A4，每页一个二维码，黄金比例布局")
        print(f"🎨 标题字体：{title_font}，验证码字体：{code_font}")
        print(f"📐 设计原理：遵循黄金比例1:1.618，优雅平衡")
        print(f"📝 包含 {len(codes_data)} 页")

    def load_codes(self) -> List[str]:
        """
        加载验证码数据
        
        Returns:
            List[str]: 验证码列表
        """
        codes_file = os.path.join(self.data_dir, "codes.json")
        if not os.path.exists(codes_file):
            print(f"❌ 验证码数据文件不存在: {codes_file}")
            return []
        
        try:
            with open(codes_file, 'r', encoding='utf-8') as f:
                codes_data = json.load(f)
            
            # 提取验证码字符串
            codes = [item['code'] for item in codes_data if isinstance(item, dict) and 'code' in item]
            print(f"✅ 加载了 {len(codes)} 个验证码")
            return codes
            
        except Exception as e:
            print(f"❌ 加载验证码数据失败: {e}")
            return []
    
    def generate_codes_array(self, codes: List[str]) -> str:
        """
        生成JavaScript验证码数组字符串
        
        Args:
            codes: 验证码列表
            
        Returns:
            str: JavaScript数组字符串
        """
        if not codes:
            return "[\n            // 验证码将通过Python脚本自动生成和更新\n        ]"
        
        # 将验证码转换为JavaScript数组格式
        codes_str = ",\n            ".join(f"'{code}'" for code in codes)
        
        return f"""[
            {codes_str}
        ]"""
    
    def update_verify_page(self):
        """
        自动更新验证页面中的验证码数组
        """
        verify_page = os.path.join(self.web_dir, "simple_verify.html")
        
        if not os.path.exists(verify_page):
            print(f"❌ 验证页面文件不存在: {verify_page}")
            return False
        
        # 加载验证码数据
        codes = self.load_codes()
        
        if not codes:
            print("❌ 没有找到验证码数据")
            return False
        
        try:
            # 读取页面内容
            with open(verify_page, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 生成新的验证码数组
            codes_array = self.generate_codes_array(codes)
            
            # 更精确的正则表达式匹配
            pattern = r'const VALID_CODES = \[\s*(?:[^[\]]*|\[[^\]]*\])*\s*\];'
            replacement = f'const VALID_CODES = {codes_array};'
            
            new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            # 检查是否成功替换
            if new_content == content:
                print("❌ 未找到要替换的验证码数组")
                print("尝试手动替换...")
                # 尝试另一种匹配方式
                pattern2 = r'const VALID_CODES = \[.*?\];'
                new_content = re.sub(pattern2, replacement, content, flags=re.DOTALL)
                
                if new_content == content:
                    print("❌ 仍然无法找到验证码数组")
                    return False
            
            # 写回文件
            with open(verify_page, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 验证页面已更新，包含 {len(codes)} 个验证码")
            return True
            
        except Exception as e:
            print(f"❌ 更新验证页面失败: {e}")
            return False
    
    def auto_deploy_web(self):
        """
        自动推送web文件夹内容到GitHub仓库根目录
        本地文件保持不变，只推送web文件夹的内容
        """
        try:
            import subprocess
            import os
            import tempfile
            import shutil
            
            print("\n🚀 自动部署web文件到GitHub...")
            
            # 检查web目录
            web_dir = "web"
            if not os.path.exists(web_dir):
                print("❌ web文件夹不存在")
                return False
            
            # 检查web文件夹是否有文件
            web_files = os.listdir(web_dir)
            if not web_files:
                print("❌ web文件夹为空")
                return False
            
            # 创建临时目录
            with tempfile.TemporaryDirectory() as temp_dir:
                print("📁 准备临时仓库...")
                
                # 初始化临时git仓库
                subprocess.run(["git", "init"], cwd=temp_dir, check=True)
                
                # 复制web文件夹内容到临时目录
                for file in web_files:
                    src = os.path.join(web_dir, file)
                    dst = os.path.join(temp_dir, file)
                    if os.path.isfile(src):
                        shutil.copy2(src, dst)
                        print(f"  📄 复制文件: {file}")
                
                # 设置git配置（如果需要）
                try:
                    subprocess.run(["git", "config", "user.email", "<EMAIL>"], 
                                 cwd=temp_dir, check=True)
                    subprocess.run(["git", "config", "user.name", "GitHub Action"], 
                                 cwd=temp_dir, check=True)
                except:
                    pass  # 如果已经配置了就忽略
                
                # 添加远程仓库
                repo_url = "https://github.com/jerrypanguo/musicqrcode_verify.git"
                subprocess.run(["git", "remote", "add", "origin", repo_url], 
                             cwd=temp_dir, check=True)
                
                # 添加所有文件
                subprocess.run(["git", "add", "."], cwd=temp_dir, check=True)
                
                # 提交
                commit_msg = f"🎯 更新验证码网站 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                subprocess.run(["git", "commit", "-m", commit_msg], 
                             cwd=temp_dir, check=True)
                
                # 强制推送到main分支
                print("🚀 推送到GitHub仓库...")
                subprocess.run(["git", "push", "-f", "origin", "HEAD:main"], 
                             cwd=temp_dir, check=True)
            
            print("✅ 已成功推送web文件到GitHub仓库")
            print("🌐 网站地址: https://jerrypanguo.github.io/musicqrcode_verify/")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Git操作失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 部署失败: {e}")
            return False
    
    def generate_batch(self, count: int = 50, orientation: str = "landscape"):
        """
        批量生成二维码
        
        Args:
            count: 生成数量，默认50个
            orientation: PDF页面方向，"landscape"(横版) 或 "portrait"(竖版)
        """
        print(f"开始生成 {count} 个验证码...")
        orientation_text = "横版" if orientation == "landscape" else "竖版"
        print(f"📄 PDF格式：{orientation_text}A4")
        
        # 生成验证码数据
        codes_data = self.generate_codes_data(count)
        
        # 保存数据
        self.save_codes_data(codes_data)
        
        # 创建PDF（支持横版和竖版）
        self.create_pdf(codes_data, orientation)
        
        # 自动更新验证页面
        print("\n正在更新验证页面...")
        self.update_verify_page()
        
        # 自动部署到GitHub
        self.auto_deploy_web()
        
        print(f"\n✅ 批量生成完成！")
        print(f"📁 二维码图片目录: {self.qrcode_dir}")
        pdf_filename = f"qrcode_sheet_{orientation_text}.pdf"
        print(f"📄 PDF文件: {os.path.join(self.output_dir, pdf_filename)}")
        print(f"💾 数据文件: {os.path.join(self.data_dir, 'codes.json')}")
        print(f"🌐 验证页面: {os.path.join('web', 'simple_verify.html')}")
        print(f"🚀 已自动推送到GitHub仓库")


def main():
    """主函数"""
    print("=== 书籍二维码生成器 ===")
    print("✅ 已配置您的 GitHub Pages 地址")
    print()
    
    # 获取用户输入
    try:
        count = int(input("请输入要生成的二维码数量 (默认50): ") or "50")
        if count <= 0:
            print("❌ 数量必须大于0")
            return
    except ValueError:
        print("❌ 请输入有效的数字")
        return
    
    # 选择PDF格式
    print("\n请选择PDF页面格式:")
    print("1. 横版A4 (29.7×21cm) - 适合横向布局，传统格式")
    print("2. 竖版A4 (21×29.7cm) - 遵循黄金比例，优雅设计")
    format_choice = input("请输入选择 (1或2，默认1): ").strip() or "1"
    
    if format_choice == "1":
        orientation = "landscape"
        print("✅ 选择横版A4格式")
    elif format_choice == "2":
        orientation = "portrait"
        print("✅ 选择竖版A4格式（黄金比例设计）")
    else:
        print("❌ 无效选择，使用默认横版格式")
        orientation = "landscape"
    
    # 获取基础URL
    print("\n请输入您的 GitHub Pages 地址:")
    print("格式: https://your-username.github.io/your-repo-name")
    base_url = input("URL (直接回车使用默认): ").strip()
    
    if not base_url:
        base_url = "https://jerrypanguo.github.io/musicqrcode_verify"
        print("✅ 使用您的GitHub Pages地址")
    
    # 创建生成器实例
    generator = QRCodeGenerator(base_url=base_url)
    
    # 批量生成
    generator.generate_batch(count, orientation)
    
    print("\n🎉 生成完成！")
    print("📋 接下来的步骤：")
    print("1. 运行程序自动推送到GitHub仓库")
    if orientation == "portrait":
        print("2. 使用生成的竖版PDF进行书籍印刷（黄金比例设计，更优雅）")
    else:
        print("2. 使用生成的横版PDF进行书籍印刷")
    print("3. 用户扫描二维码即可验证真伪")
    print(f"4. 二维码指向: {base_url}/simple_verify.html")


if __name__ == "__main__":
    main() 