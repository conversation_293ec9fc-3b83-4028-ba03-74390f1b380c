# 乐谱验证系统 - 服务器迁移指南

## 🎯 迁移概述

本指南详细说明如何将乐谱验证系统从一台VPS迁移到另一台VPS，确保数据完整性和服务连续性。

## 📋 迁移前准备

### 1. 迁移清单

**需要迁移的数据**：
- ✅ 数据库文件（授权码数据）
- ✅ 配置文件（API密钥、管理员账号）
- ✅ 日志文件（可选）
- ✅ 备份文件（可选）
- ✅ SSL证书（如果使用Let's Encrypt）

**需要准备的信息**：
- 🔑 新服务器IP地址
- 🌐 域名管理面板访问权限
- 📧 SSL证书申请邮箱
- 🔐 管理员账号密码

### 2. 新服务器要求

- **操作系统**: Ubuntu 22.04 LTS
- **配置**: 与原服务器相同或更高
- **网络**: 公网IP地址
- **权限**: root或sudo访问权限

## 🔄 完整迁移流程

### 第一步：备份原服务器数据

#### 1.1 创建数据备份

```bash
# 连接到原服务器
ssh musicqr@old-server-ip

# 创建备份目录
mkdir -p ~/migration_backup
cd ~/migration_backup

# 备份数据库
sudo cp /var/lib/musicqr/musicqr.db ./musicqr.db
sudo chown musicqr:musicqr ./musicqr.db

# 备份配置文件
sudo cp /var/www/musicqr/.env ./env_config

# 备份应用代码（如果有自定义修改）
sudo cp -r /var/www/musicqr ./app_backup

# 备份Nginx配置
sudo cp /etc/nginx/sites-available/musicqr ./nginx_config

# 备份systemd服务配置
sudo cp /etc/systemd/system/musicqr-api.service ./systemd_config

# 备份日志文件（可选）
sudo cp -r /var/log/musicqr ./logs_backup

# 备份历史备份文件（可选）
sudo cp -r /var/backups/musicqr ./historical_backups
```

#### 1.2 导出数据库统计

```bash
# 导出数据库统计信息
cd /var/www/musicqr
source venv/bin/activate

python3 -c "
import sqlite3
conn = sqlite3.connect('/var/lib/musicqr/musicqr.db')
cursor = conn.cursor()

# 获取统计信息
cursor.execute('SELECT COUNT(*) FROM auth_codes')
total = cursor.fetchone()[0]

cursor.execute('SELECT COUNT(*) FROM auth_codes WHERE activated = 1')
activated = cursor.fetchone()[0]

print(f'总授权码数: {total}')
print(f'已激活数: {activated}')
print(f'激活率: {activated/total*100:.1f}%' if total > 0 else '激活率: 0%')

conn.close()
" > ~/migration_backup/db_stats.txt

echo "数据库统计信息已保存到 db_stats.txt"
```

#### 1.3 创建迁移包

```bash
# 创建完整的迁移包
cd ~/migration_backup
tar -czf migration_package_$(date +%Y%m%d_%H%M%S).tar.gz \
    musicqr.db \
    env_config \
    nginx_config \
    systemd_config \
    db_stats.txt \
    app_backup/ \
    logs_backup/ \
    historical_backups/

echo "迁移包创建完成："
ls -lh migration_package_*.tar.gz
```

#### 1.4 下载迁移包到本地

```bash
# 在本地机器上执行
scp musicqr@old-server-ip:~/migration_backup/migration_package_*.tar.gz ./

echo "迁移包已下载到本地"
```

### 第二步：在新服务器上部署系统

#### 2.1 基础部署

```bash
# 连接到新服务器
ssh root@new-server-ip

# 创建用户
adduser musicqr
usermod -aG sudo musicqr
su - musicqr

# 上传项目文件和迁移包
# scp -r 乐谱验证系统-VPS版/ musicqr@new-server-ip:/home/<USER>/
# scp migration_package_*.tar.gz musicqr@new-server-ip:/home/<USER>/

# 运行基础部署
cd ~/乐谱验证系统-VPS版
chmod +x server/deploy/setup.sh
sudo ./server/deploy/setup.sh
```

#### 2.2 恢复数据

```bash
# 解压迁移包
cd ~
tar -xzf migration_package_*.tar.gz

# 停止新部署的服务
sudo systemctl stop musicqr-api

# 恢复数据库
sudo cp musicqr.db /var/lib/musicqr/musicqr.db
sudo chown musicqr:musicqr /var/lib/musicqr/musicqr.db

# 恢复配置文件
sudo cp env_config /var/www/musicqr/.env
sudo chown musicqr:musicqr /var/www/musicqr/.env
sudo chmod 600 /var/www/musicqr/.env

# 验证配置
echo "恢复的配置信息："
sudo cat /var/www/musicqr/.env | grep -E "(SECRET_KEY|ADMIN_)"
```

#### 2.3 更新配置

```bash
# 如果需要更新某些配置
sudo nano /var/www/musicqr/.env

# 例如更新数据库路径、日志路径等
# DATABASE_PATH=/var/lib/musicqr/musicqr.db
# LOG_FILE=/var/log/musicqr/api.log
# BACKUP_DIR=/var/backups/musicqr
```

### 第三步：更新DNS和SSL

#### 3.1 更新DNS解析

```bash
# 在域名管理面板中更新A记录
# 类型: A
# 名称: verify.yuzeguitar.me
# 值: new-server-ip
# TTL: 300 (5分钟，便于快速生效)

# 验证DNS解析
nslookup verify.yuzeguitar.me
dig verify.yuzeguitar.me

# 等待DNS传播（通常5-30分钟）
```

#### 3.2 申请新的SSL证书

```bash
# 在新服务器上申请SSL证书
sudo certbot --nginx -d verify.yuzeguitar.me --email <EMAIL> --agree-tos --non-interactive

# 验证SSL证书
sudo certbot certificates

# 测试自动续期
sudo certbot renew --dry-run
```

### 第四步：启动和验证服务

#### 4.1 启动服务

```bash
# 启动API服务
sudo systemctl start musicqr-api
sudo systemctl enable musicqr-api

# 重启Nginx
sudo systemctl restart nginx

# 检查服务状态
sudo systemctl status musicqr-api --no-pager
sudo systemctl status nginx --no-pager
```

#### 4.2 验证数据完整性

```bash
# 验证数据库数据
cd /var/www/musicqr
source venv/bin/activate

python3 -c "
import sqlite3
conn = sqlite3.connect('/var/lib/musicqr/musicqr.db')
cursor = conn.cursor()

cursor.execute('SELECT COUNT(*) FROM auth_codes')
total = cursor.fetchone()[0]

cursor.execute('SELECT COUNT(*) FROM auth_codes WHERE activated = 1')
activated = cursor.fetchone()[0]

print(f'迁移后统计:')
print(f'总授权码数: {total}')
print(f'已激活数: {activated}')
print(f'激活率: {activated/total*100:.1f}%' if total > 0 else '激活率: 0%')

conn.close()
"

# 对比原服务器的统计信息
echo "原服务器统计信息："
cat ~/db_stats.txt
```

#### 4.3 功能测试

```bash
# 测试API接口
curl https://verify.yuzeguitar.me/api/status

# 测试前端页面
curl -I https://verify.yuzeguitar.me/

# 测试管理后台
curl -I https://verify.yuzeguitar.me/admin

# 运行完整系统测试
cd ~/乐谱验证系统-VPS版
python3 scripts/test_system.py --url https://verify.yuzeguitar.me
```

### 第五步：更新客户端配置

#### 5.1 获取新的API密钥

```bash
# 在新服务器上获取API密钥
cd /var/www/musicqr
source venv/bin/activate

python3 -c "
import hashlib
import hmac

with open('.env', 'r') as f:
    for line in f:
        if line.startswith('SECRET_KEY='):
            secret_key = line.split('=', 1)[1].strip()
            break

salt = 'musicqr_api_salt_2024'
api_key = hmac.new(secret_key.encode(), salt.encode(), hashlib.sha256).hexdigest()

print('新服务器配置信息:')
print(f'SECRET_KEY: {secret_key}')
print(f'API_KEY: {api_key}')
print()
print('客户端配置命令:')
print(f'export CLIENT_SECRET_KEY=\"{secret_key}\"')
print(f'export VPS_URL=\"https://verify.yuzeguitar.me\"')
print(f'export API_KEY_SALT=\"{salt}\"')
"
```

#### 5.2 测试客户端连接

```bash
# 在本地客户端测试
export CLIENT_SECRET_KEY="your-secret-key"
export VPS_URL="https://verify.yuzeguitar.me"
export API_KEY_SALT="musicqr_api_salt_2024"

# 测试连接
cd 乐谱验证系统-VPS版/client
python3 -c "
from generate_codes import VPSQRCodeGenerator
generator = VPSQRCodeGenerator()
success, message = generator.check_vps_connection()
print(message)
"
```

## 🔄 零停机迁移（高级）

如果需要实现零停机迁移，可以使用以下策略：

### 1. 双服务器运行

```bash
# 在新服务器上部署系统但使用不同域名
# 例如: new.verify.yuzeguitar.me

# 同步数据到新服务器
# 使用数据库复制或定期同步脚本

# 测试新服务器功能完整性
```

### 2. 流量切换

```bash
# 方法1: DNS切换（有传播延迟）
# 直接更新DNS记录指向新服务器

# 方法2: 负载均衡器切换（推荐）
# 使用Cloudflare或其他CDN服务
# 逐步将流量从旧服务器切换到新服务器

# 方法3: 反向代理切换
# 在旧服务器上配置反向代理指向新服务器
```

## 🛡️ 迁移后安全检查

### 1. 安全配置验证

```bash
# 检查防火墙状态
sudo ufw status

# 检查SSL证书
sudo certbot certificates

# 检查服务权限
ls -la /var/www/musicqr/
ls -la /var/lib/musicqr/

# 检查配置文件权限
ls -la /var/www/musicqr/.env
```

### 2. 性能测试

```bash
# 压力测试API接口
for i in {1..100}; do
    curl -s https://verify.yuzeguitar.me/api/status > /dev/null &
done
wait

# 检查系统资源使用
htop
df -h
free -h
```

## 📊 迁移后维护

### 1. 监控设置

```bash
# 设置日志轮转
sudo tee /etc/logrotate.d/musicqr << EOF
/var/log/musicqr/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 musicqr musicqr
    postrotate
        systemctl reload musicqr-api
    endscript
}
EOF

# 设置定时备份
(crontab -l 2>/dev/null; echo "0 2 * * * cp /var/lib/musicqr/musicqr.db /var/backups/musicqr/backup_\$(date +\%Y\%m\%d).db") | crontab -
```

### 2. 清理原服务器

```bash
# 确认新服务器运行正常后，清理原服务器
# 保留备份文件一段时间（建议30天）

# 停止原服务器服务
sudo systemctl stop musicqr-api nginx

# 备份重要数据到其他位置
# 然后可以安全地删除或重新配置原服务器
```

## 🐛 迁移故障排除

### 常见问题

1. **数据库迁移失败**
   ```bash
   # 检查数据库文件权限
   ls -la /var/lib/musicqr/musicqr.db
   
   # 重新设置权限
   sudo chown musicqr:musicqr /var/lib/musicqr/musicqr.db
   ```

2. **SSL证书申请失败**
   ```bash
   # 检查DNS解析
   nslookup verify.yuzeguitar.me
   
   # 检查防火墙
   sudo ufw status
   
   # 手动申请证书
   sudo certbot certonly --webroot -w /var/www/musicqr/web -d verify.yuzeguitar.me
   ```

3. **服务启动失败**
   ```bash
   # 查看详细错误日志
   sudo journalctl -u musicqr-api -n 50
   
   # 检查配置文件
   cat /var/www/musicqr/.env
   ```

## 📞 迁移支持

如果在迁移过程中遇到问题：
- **开发者**: Yuze Pan
- **微信**: Guitar_yuze
- **建议**: 先在测试环境进行迁移演练

---

**成功迁移后，你的乐谱验证系统将在新服务器上稳定运行！** 🎉
