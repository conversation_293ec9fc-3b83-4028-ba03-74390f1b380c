# 乐谱验证系统 - Git忽略文件

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# 项目特定文件
# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/

# 输出文件
output/
*.pdf

# 配置文件（包含敏感信息）
.env
config.ini
secrets.json

# 备份文件
backup/
*.backup
*.bak

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 证书文件
*.pem
*.key
*.crt
*.p12

# 测试报告
test_report_*.json
coverage_report/

# 部署相关
deployment.log
install.log

# 字体文件（如果很大的话）
# fonts/*.ttf
# fonts/*.otf

# 用户数据
user_data/
uploads/

# 缓存文件
.cache/
*.cache

# 编译文件
*.pyc
*.pyo
*.pyd

# 包文件
*.tar.gz
*.zip
*.rar

# 本地开发配置
local_config.py
dev_settings.py
