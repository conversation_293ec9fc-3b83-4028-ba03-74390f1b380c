# 乐谱验证系统 - 完整部署指南

## 🎯 部署概述

本指南将帮助你在全新的Ubuntu 22.04 VPS上部署乐谱二维码验证系统，包括服务器迁移的完整流程。

## 📋 系统要求

### 硬件要求
- **CPU**: 1核心（推荐2核心）
- **内存**: 1GB（推荐2GB）
- **存储**: 10GB可用空间（推荐20GB）
- **网络**: 公网IP地址

### 软件要求
- **操作系统**: Ubuntu 22.04 LTS
- **域名**: 用于HTTPS访问（可选但推荐）
- **SSH访问**: 管理员权限

## 🚀 一键部署（推荐）

### 1. 准备工作

```bash
# 连接到VPS
ssh root@your-vps-ip

# 更新系统
apt update && apt upgrade -y

# 创建应用用户
adduser musicqr
usermod -aG sudo musicqr
su - musicqr
```

### 2. 下载项目

```bash
# 方法1: 直接下载（如果有压缩包）
wget https://your-domain.com/musicqr-vps.tar.gz
tar -xzf musicqr-vps.tar.gz
cd 乐谱验证系统-VPS版

# 方法2: 使用git（如果有仓库）
git clone https://github.com/your-repo/musicqr-vps.git
cd musicqr-vps

方法3: 手动上传
使用scp上传整个项目目录
scp -r 乐谱验证系统-VPS版/ musicqr@your-vps-ip:/home/<USER>/
```

### 3. 运行一键部署脚本

```bash
# 进入项目目录
cd 乐谱验证系统-VPS版

# 运行部署脚本
chmod +x server/deploy/setup.sh
sudo ./server/deploy/setup.sh
```

部署脚本将自动完成：
- ✅ 安装系统依赖
- ✅ 配置Python环境
- ✅ 部署应用代码
- ✅ 配置数据库
- ✅ 设置systemd服务
- ✅ 配置Nginx
- ✅ 设置防火墙
- ✅ 启动所有服务

### 4. 配置域名和SSL

```bash
# 配置域名DNS解析
# 在域名管理面板添加A记录：
# 类型: A
# 名称: verify.yuzeguitar.me
# 值: your-vps-ip

# 等待DNS生效后，配置SSL证书
sudo certbot --nginx -d verify.yuzeguitar.me
```

## 🔧 手动部署（详细步骤）

如果一键部署失败，可以按以下步骤手动部署：

### 1. 安装系统依赖

```bash
sudo apt update
sudo apt install -y python3 python3-pip python3-venv nginx sqlite3 git curl wget unzip htop ufw certbot python3-certbot-nginx
```

### 2. 创建目录结构

```bash
sudo mkdir -p /var/www/musicqr
sudo mkdir -p /var/lib/musicqr
sudo mkdir -p /var/log/musicqr
sudo mkdir -p /var/backups/musicqr

sudo chown -R musicqr:musicqr /var/www/musicqr
sudo chown -R musicqr:musicqr /var/lib/musicqr
sudo chown -R musicqr:musicqr /var/log/musicqr
sudo chown -R musicqr:musicqr /var/backups/musicqr
```

### 3. 部署应用代码

```bash
# 复制服务器代码
cp -r server/* /var/www/musicqr/
cp -r web/ /var/www/musicqr/

# 设置Python虚拟环境
cd /var/www/musicqr
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. 配置环境变量

```bash
# 生成随机密钥
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")

# 创建配置文件
cat > /var/www/musicqr/.env << EOF
FLASK_ENV=production
SECRET_KEY=$SECRET_KEY
API_KEY_SALT=musicqr_api_salt_2024
DATABASE_PATH=/var/lib/musicqr/musicqr.db
LOG_FILE=/var/log/musicqr/api.log
BACKUP_DIR=/var/backups/musicqr
ADMIN_USERNAME=admin
ADMIN_PASSWORD=musicqr2024
EOF

chmod 600 /var/www/musicqr/.env
echo "生成的SECRET_KEY: $SECRET_KEY"
```

### 5. 初始化数据库

```bash
cd /var/www/musicqr
source venv/bin/activate
python3 -c "from models import init_db; init_db('/var/lib/musicqr/musicqr.db')"
```

### 6. 配置systemd服务

```bash
sudo cp server/deploy/systemd.service /etc/systemd/system/musicqr-api.service

# 或手动创建
sudo tee /etc/systemd/system/musicqr-api.service > /dev/null << EOF
[Unit]
Description=Music QR Code Verification API
After=network.target

[Service]
Type=exec
User=musicqr
Group=musicqr
WorkingDirectory=/var/www/musicqr
Environment=PATH=/var/www/musicqr/venv/bin
EnvironmentFile=/var/www/musicqr/.env
ExecStart=/var/www/musicqr/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 2 app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable musicqr-api
sudo systemctl start musicqr-api
```

### 7. 配置Nginx

```bash
sudo cp server/deploy/nginx.conf /etc/nginx/sites-available/musicqr

# 或手动创建
sudo tee /etc/nginx/sites-available/musicqr > /dev/null << 'EOF'
server {
    listen 80;
    server_name verify.yuzeguitar.me;
    
    # 管理后台路由
    location /admin {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # API路由
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 前端静态文件
    location / {
        root /var/www/musicqr/web;
        try_files $uri $uri/ /index.html;
        
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    access_log /var/log/nginx/musicqr_access.log;
    error_log /var/log/nginx/musicqr_error.log;
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/musicqr /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t
sudo systemctl restart nginx
```

### 8. 配置防火墙

```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw --force enable
```

## 🔄 服务器迁移指南

### 1. 备份原服务器数据

```bash
# 在原服务器上执行
cd /var/www/musicqr

# 备份数据库
cp /var/lib/musicqr/musicqr.db ./musicqr_backup.db

# 备份配置文件
cp .env env_backup

# 创建完整备份
tar -czf musicqr_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
    musicqr_backup.db \
    env_backup \
    /var/log/musicqr/ \
    /var/backups/musicqr/

# 下载备份文件到本地
scp musicqr@old-server-ip:/var/www/musicqr/musicqr_backup_*.tar.gz ./
```

### 2. 在新服务器上部署

```bash
# 按照上述部署步骤在新服务器上部署系统

# 上传备份文件
scp musicqr_backup_*.tar.gz musicqr@new-server-ip:/home/<USER>/

# 在新服务器上解压
tar -xzf musicqr_backup_*.tar.gz
```

### 3. 恢复数据

```bash
# 停止服务
sudo systemctl stop musicqr-api

# 恢复数据库
cp musicqr_backup.db /var/lib/musicqr/musicqr.db
chown musicqr:musicqr /var/lib/musicqr/musicqr.db

# 恢复配置（可选，或使用新配置）
cp env_backup /var/www/musicqr/.env

# 启动服务
sudo systemctl start musicqr-api
```

### 4. 更新DNS解析

```bash
# 在域名管理面板更新A记录
# 将域名指向新服务器IP
# 等待DNS传播（通常5-30分钟）

# 验证DNS解析
nslookup verify.yuzeguitar.me
```

### 5. 重新申请SSL证书

```bash
# 在新服务器上申请SSL证书
sudo certbot --nginx -d verify.yuzeguitar.me
```

## ✅ 部署验证

### 1. 检查服务状态

```bash
# 检查API服务
sudo systemctl status musicqr-api

# 检查Nginx服务
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep -E ':(80|443|5000)'
```

### 2. 测试功能

```bash
# 测试API接口
curl http://localhost:5000/api/status
curl https://verify.yuzeguitar.me/api/status

# 测试前端页面
curl -I https://verify.yuzeguitar.me/

# 测试管理后台
curl -I https://verify.yuzeguitar.me/admin
```

### 3. 运行系统测试

```bash
# 使用测试脚本
cd /home/<USER>/乐谱验证系统-VPS版
python3 scripts/test_system.py --url https://verify.yuzeguitar.me
```

## 🔧 配置客户端

### 1. 获取API密钥

```bash
# 在VPS上获取密钥
cd /var/www/musicqr
cat .env | grep SECRET_KEY
```

### 2. 配置本地客户端

```bash
# 在本地客户端目录
export VPS_URL='https://verify.yuzeguitar.me'
export CLIENT_SECRET_KEY='your-secret-key-from-server'
export API_KEY_SALT='musicqr_api_salt_2024'

# 测试连接
python3 generate_codes.py
```

## 📊 系统管理

### 日常维护命令

```bash
# 查看服务状态
sudo systemctl status musicqr-api nginx

# 查看日志
sudo journalctl -u musicqr-api -f
sudo tail -f /var/log/nginx/musicqr_access.log

# 重启服务
sudo systemctl restart musicqr-api nginx

# 备份数据库
cp /var/lib/musicqr/musicqr.db /var/backups/musicqr/backup_$(date +%Y%m%d).db
```

### 更新系统

```bash
# 更新代码
cd /home/<USER>/乐谱验证系统-VPS版
git pull  # 如果使用git

# 运行更新脚本
chmod +x server/deploy/update.sh
./server/deploy/update.sh
```

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   sudo journalctl -u musicqr-api -n 50
   ```

2. **Nginx配置错误**
   ```bash
   sudo nginx -t
   sudo tail -f /var/log/nginx/error.log
   ```

3. **数据库权限问题**
   ```bash
   sudo chown musicqr:musicqr /var/lib/musicqr/musicqr.db
   ```

4. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :5000
   ```

### 紧急恢复

```bash
# 如果系统完全无法访问，使用备份恢复
sudo systemctl stop musicqr-api nginx
cp /var/backups/musicqr/latest_backup.db /var/lib/musicqr/musicqr.db
sudo systemctl start musicqr-api nginx
```

## 📞 技术支持

如有问题，请联系：
- **开发者**: Yuze Pan
- **微信**: Guitar_yuze
- **版本**: v2.0.0

---

**部署成功后，你将拥有一个完整的乐谱验证系统！** 🎉
