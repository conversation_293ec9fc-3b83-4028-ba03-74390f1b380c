/* 乐谱验证系统样式文件 - VPS版本 - 极简黑白设计 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', 'Times', serif;
    background: #ffffff;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #000000;
    line-height: 1.6;
}

.container {
    max-width: 500px;
    width: 100%;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 2.2em;
    margin-bottom: 10px;
    font-weight: 300;
    letter-spacing: 1px;
    color: #000000;
}

.subtitle {
    font-size: 1em;
    color: #666666;
    font-weight: 300;
}

.card {
    background: #ffffff;
    border: 1px solid #000000;
    padding: 40px 30px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    animation: fadeIn 0.3s ease-out;
}

.hidden {
    display: none;
}

/* 加载动画 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 2px solid #f0f0f0;
    border-top: 2px solid #000000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-detail {
    font-size: 0.9em;
    color: #666666;
    margin-top: -10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态指示 */
.success-icon, .error-icon, .info-icon {
    font-size: 3em;
    margin-bottom: 20px;
    font-weight: 300;
}

.success-icon {
    color: #000000;
}

.error-icon {
    color: #000000;
}

.info-icon {
    color: #000000;
}

/* 标题和文本 */
h2 {
    font-size: 1.8em;
    margin-bottom: 15px;
    color: #000000;
    font-weight: 300;
}

h3 {
    font-size: 1.4em;
    margin-bottom: 10px;
    color: #000000;
    font-weight: 300;
}

p {
    font-size: 1em;
    line-height: 1.6;
    margin-bottom: 15px;
    color: #333333;
}

/* 信息区域 */
.book-info {
    background: #f8f8f8;
    border: 1px solid #e0e0e0;
    padding: 20px;
    margin: 20px 0;
    text-align: left;
}

.book-info p {
    margin-bottom: 8px;
    color: #000000;
}

.book-info strong {
    color: #000000;
    font-weight: 500;
}

/* 激活信息 */
.activation-info {
    background: #f0f0f0;
    border: 1px solid #ddd;
    padding: 15px;
    margin-top: 15px;
    border-radius: 3px;
}

.activation-info p {
    margin-bottom: 5px;
    color: #000000;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: 2px solid #000000;
    background: #ffffff;
    color: #000000;
    font-size: 1em;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    margin: 5px;
    font-family: inherit;
}

.btn:hover {
    background: #000000;
    color: #ffffff;
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background: #000000;
    color: #ffffff;
}

.btn-primary:hover {
    background: #333333;
    border-color: #333333;
}

.btn-secondary {
    background: #ffffff;
    color: #000000;
}

.btn-secondary:hover {
    background: #000000;
    color: #ffffff;
}

/* 操作按钮组 */
.action-buttons {
    margin-top: 20px;
}

.action-buttons .btn {
    margin: 5px 10px;
}

/* 输入组 */
.input-group {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    align-items: center;
}

.input-group input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #000000;
    background: #ffffff;
    color: #000000;
    font-size: 1em;
    font-family: 'Courier', monospace;
    font-weight: bold;
}

.input-group input:focus {
    outline: none;
    border-color: #333333;
    background: #f8f8f8;
}

.input-group input::placeholder {
    color: #999999;
    font-weight: normal;
}

/* 错误详情 */
.error-details {
    background: #f8f8f8;
    border: 1px solid #000000;
    padding: 20px;
    margin: 20px 0;
    text-align: left;
}

.error-details p {
    color: #000000;
    font-weight: 500;
    margin-bottom: 10px;
}

.error-details ul {
    margin-left: 20px;
}

.error-details li {
    color: #000000;
    margin-bottom: 5px;
}

/* 注释文本 */
.note {
    font-size: 0.9em;
    color: #666666;
    font-style: italic;
}

/* 页脚 */
.footer {
    text-align: center;
    color: #666666;
    margin-top: 40px;
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
}

.footer p {
    margin-bottom: 5px;
    color: #666666;
}

.footer small {
    font-size: 0.8em;
}

.tech-info {
    margin-top: 10px;
    font-size: 0.75em;
    color: #999999;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 1.8em;
    }
    
    .card {
        padding: 30px 20px;
    }
    
    h2 {
        font-size: 1.5em;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9em;
    }
    
    .success-icon, .error-icon, .info-icon {
        font-size: 2.5em;
    }
    
    .input-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .input-group input {
        width: 100%;
    }
    
    .action-buttons .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.6em;
    }
    
    .card {
        padding: 25px 15px;
    }
    
    .book-info {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.85em;
    }
}

/* 简约动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态的脉冲效果 */
.loading p {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
}

/* 成功状态的弹跳效果 */
.success-icon {
    animation: bounce 0.6s ease-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* 错误状态的摇摆效果 */
.error-icon {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}
