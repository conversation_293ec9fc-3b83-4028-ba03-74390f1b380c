# 乐谱验证系统 - 使用说明

## 🎯 系统概述

乐谱二维码验证系统VPS版本是一个完整的正版乐谱验证解决方案，包含本地客户端、VPS服务器和Web管理界面。

## 👥 用户角色

### 1. 🎵 乐谱制作者（客户端用户）
- 使用本地Python客户端生成带二维码的乐谱PDF
- 管理授权码和二维码生成

### 2. 🎼 乐谱购买者（最终用户）
- 扫描二维码验证乐谱真伪
- 通过Web页面查看验证结果

### 3. 🛠️ 系统管理员
- 通过Web管理后台管理系统
- 监控系统状态和数据统计

## 📱 客户端使用指南

### 安装和配置

#### 1. 环境准备

```bash
# 确保Python 3.8+已安装
python3 --version

# 安装依赖
cd 乐谱验证系统-VPS版/client
pip install -r requirements.txt
```

#### 2. 配置VPS连接

```bash
# 设置环境变量
export VPS_URL='https://verify.yuzeguitar.me'
export CLIENT_SECRET_KEY='your-secret-key-from-server'
export API_KEY_SALT='musicqr_api_salt_2024'

# 或创建配置文件
cat > config.py << EOF
VPS_URL = 'https://verify.yuzeguitar.me'
CLIENT_SECRET_KEY = 'your-secret-key-from-server'
API_KEY_SALT = 'musicqr_api_salt_2024'
EOF
```

### 基本使用

#### 1. 生成单个二维码

```bash
# 基本用法
python3 generate_codes.py

# 指定授权码
python3 generate_codes.py --code ABCD12345678

# 选择布局
python3 generate_codes.py --layout horizontal  # 横版
python3 generate_codes.py --layout vertical    # 竖版
```

#### 2. 批量生成

```bash
# 生成10个授权码
python3 generate_codes.py --batch 10

# 生成100个授权码（横版）
python3 generate_codes.py --batch 100 --layout horizontal
```

#### 3. 从文件导入

```bash
# 从文本文件导入授权码
python3 generate_codes.py --import codes.txt

# 文件格式（每行一个授权码）：
# ABCD12345678
# EFGH87654321
# IJKL13579246
```

### 高级功能

#### 1. 自定义设置

```python
# 在generate_codes.py中修改
TITLE_TEXT = "您的乐谱标题"
SUBTITLE_TEXT = "作者信息"
FOOTER_TEXT = "版权信息"
```

#### 2. 字体配置

```bash
# 将字体文件放入fonts目录
cp "Your Font.ttf" client/fonts/

# 在代码中指定字体
FONT_PATH = "fonts/Your Font.ttf"
```

#### 3. 输出设置

```python
# 修改输出目录
OUTPUT_DIR = "output"

# 修改文件名格式
filename = f"qr_code_{code}_{timestamp}.pdf"
```

## 🌐 Web界面使用指南

### 用户验证页面

#### 访问地址
```
https://verify.yuzeguitar.me/
```

#### 使用步骤
1. **扫描二维码**：使用手机扫描乐谱上的二维码
2. **自动跳转**：浏览器自动打开验证页面
3. **查看结果**：页面显示验证结果和乐谱信息
4. **手动输入**：也可手动输入12位授权码验证

#### 验证结果说明
- ✅ **正版乐谱**：显示绿色，授权码有效
- ❌ **无效授权码**：显示红色，授权码不存在或已失效
- ⚠️ **网络错误**：显示黄色，请检查网络连接

### 管理后台使用

#### 访问地址
```
https://verify.yuzeguitar.me/admin
```

#### 登录信息
- **用户名**：admin
- **密码**：部署时生成的密码（查看服务器.env文件）

#### 主要功能

##### 1. 📊 仪表板
- **系统统计**：总授权码数、激活率、今日查询
- **趋势图表**：7天激活趋势可视化
- **最近活动**：最新激活的授权码列表
- **系统状态**：服务运行状态和资源使用

##### 2. 🔑 授权码管理
- **列表查看**：分页显示所有授权码
- **搜索功能**：按授权码搜索
- **状态筛选**：筛选已激活/未激活
- **批量操作**：批量删除、导出
- **详情查看**：查看单个授权码完整信息

##### 3. ➕ 添加授权码
- **单个添加**：手动输入或自动生成
- **批量生成**：一次生成多个授权码
- **文件导入**：从CSV或文本文件导入
- **重复检测**：自动检测重复授权码

##### 4. 📊 数据导出
- **CSV格式**：导出为Excel可读格式
- **选择性导出**：导出指定授权码
- **完整信息**：包含所有字段数据

##### 5. ⚙️ 系统信息
- **服务器状态**：CPU、内存、磁盘使用
- **数据库信息**：文件大小、记录数量
- **应用信息**：版本、配置、安全设置
- **维护工具**：系统检查、日志查看

## 🔧 管理员操作指南

### 日常维护

#### 1. 检查系统状态

```bash
# 检查服务状态
sudo systemctl status musicqr-api nginx

# 查看系统资源
htop
df -h
free -h

# 检查日志
sudo journalctl -u musicqr-api -f
tail -f /var/log/nginx/musicqr_access.log
```

#### 2. 数据备份

```bash
# 手动备份
/var/www/musicqr/backup.sh

# 查看备份文件
ls -la /var/backups/musicqr/

# 恢复备份
cp /var/backups/musicqr/backup_20240101.db /var/lib/musicqr/musicqr.db
sudo systemctl restart musicqr-api
```

#### 3. 更新系统

```bash
# 更新应用代码
cd /home/<USER>/乐谱验证系统-VPS版
git pull  # 如果使用git

# 运行更新脚本
./server/deploy/update.sh

# 重启服务
sudo systemctl restart musicqr-api nginx
```

### 配置管理

#### 1. 修改管理员密码

```bash
# 编辑配置文件
sudo nano /var/www/musicqr/.env

# 修改密码
ADMIN_PASSWORD=new_secure_password

# 重启服务
sudo systemctl restart musicqr-api
```

#### 2. 配置SSL证书

```bash
# 申请Let's Encrypt证书
sudo certbot --nginx -d verify.yuzeguitar.me

# 检查证书状态
sudo certbot certificates

# 测试自动续期
sudo certbot renew --dry-run
```

#### 3. 性能优化

```bash
# 调整Nginx配置
sudo nano /etc/nginx/sites-available/musicqr

# 调整应用配置
sudo nano /etc/systemd/system/musicqr-api.service

# 重启服务
sudo systemctl daemon-reload
sudo systemctl restart musicqr-api nginx
```

## 📊 数据统计和分析

### 通过管理后台查看

1. **登录管理后台**
2. **访问仪表板**：查看实时统计
3. **导出数据**：下载CSV文件进行分析
4. **查看趋势**：观察激活趋势图表

### 通过数据库查询

```sql
-- 连接数据库
sqlite3 /var/lib/musicqr/musicqr.db

-- 查看总体统计
SELECT 
    COUNT(*) as total_codes,
    COUNT(CASE WHEN activated = 1 THEN 1 END) as activated_codes,
    ROUND(COUNT(CASE WHEN activated = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as activation_rate
FROM auth_codes;

-- 查看每日激活统计
SELECT 
    DATE(activation_date) as date,
    COUNT(*) as activations
FROM auth_codes 
WHERE activated = 1 
GROUP BY DATE(activation_date)
ORDER BY date DESC;

-- 查看最活跃的授权码
SELECT code, query_count, activation_date, last_query_date
FROM auth_codes 
WHERE activated = 1 
ORDER BY query_count DESC 
LIMIT 10;
```

## 🐛 常见问题解决

### 客户端问题

#### 1. 无法连接VPS
```bash
# 检查网络连接
ping verify.yuzeguitar.me

# 检查API密钥
echo $CLIENT_SECRET_KEY

# 测试API接口
curl https://verify.yuzeguitar.me/api/status
```

#### 2. 字体显示问题
```bash
# 检查字体文件
ls -la client/fonts/

# 使用系统字体
# 修改generate_codes.py中的FONT_PATH
```

#### 3. PDF生成失败
```bash
# 检查依赖
pip install --upgrade reportlab qrcode pillow

# 检查输出目录权限
chmod 755 client/output/
```

### 服务器问题

#### 1. 服务启动失败
```bash
# 查看错误日志
sudo journalctl -u musicqr-api -n 50

# 检查配置文件
cat /var/www/musicqr/.env

# 检查端口占用
sudo netstat -tlnp | grep :5000
```

#### 2. 数据库问题
```bash
# 检查数据库文件
ls -la /var/lib/musicqr/musicqr.db

# 检查权限
sudo chown musicqr:musicqr /var/lib/musicqr/musicqr.db

# 重新初始化
cd /var/www/musicqr
source venv/bin/activate
python3 -c "from models import init_db; init_db('/var/lib/musicqr/musicqr.db')"
```

#### 3. Nginx配置问题
```bash
# 测试配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 重新加载配置
sudo systemctl reload nginx
```

## 📞 技术支持

### 联系方式
- **开发者**：Yuze Pan
- **微信**：Guitar_yuze
- **版本**：v2.0.0

### 获取帮助
1. **查看日志**：首先检查系统日志
2. **运行测试**：使用test_system.py诊断问题
3. **查看文档**：参考部署指南和故障排除
4. **联系支持**：提供详细的错误信息和日志

---

**祝您使用愉快！让正版乐谱验证变得简单而优雅** 🎵
