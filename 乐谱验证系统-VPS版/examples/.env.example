# 乐谱验证系统 - 环境变量配置示例
# 复制此文件为 .env 并修改相应配置

# ===========================================
# 基础配置
# ===========================================

# Flask运行环境 (development/production)
FLASK_ENV=production

# 应用密钥 (用于会话加密，请使用随机生成的32位十六进制字符串)
SECRET_KEY=your-32-character-hex-secret-key-here

# API密钥盐值 (用于生成API密钥，建议不要修改)
API_KEY_SALT=musicqr_api_salt_2024

# ===========================================
# 数据库配置
# ===========================================

# 数据库文件路径
DATABASE_PATH=/var/lib/musicqr/musicqr.db

# 数据库备份目录
BACKUP_DIR=/var/backups/musicqr

# ===========================================
# 日志配置
# ===========================================

# 日志文件路径
LOG_FILE=/var/log/musicqr/api.log

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# ===========================================
# 管理后台配置
# ===========================================

# 管理员用户名
ADMIN_USERNAME=admin

# 管理员密码 (建议使用强密码)
ADMIN_PASSWORD=your-secure-admin-password

# 会话超时时间 (分钟)
SESSION_TIMEOUT=60

# ===========================================
# 服务器配置
# ===========================================

# 服务器主机地址
HOST=127.0.0.1

# 服务器端口
PORT=5000

# 工作进程数 (建议设置为CPU核心数)
WORKERS=2

# 请求超时时间 (秒)
TIMEOUT=30

# ===========================================
# 安全配置
# ===========================================

# 允许的域名 (多个域名用逗号分隔)
ALLOWED_HOSTS=verify.yuzeguitar.me,localhost

# CORS允许的源 (多个源用逗号分隔)
CORS_ORIGINS=https://verify.yuzeguitar.me,http://localhost:3000

# API请求频率限制 (每分钟最大请求数)
RATE_LIMIT=100

# ===========================================
# 邮件配置 (可选)
# ===========================================

# SMTP服务器
SMTP_SERVER=smtp.gmail.com

# SMTP端口
SMTP_PORT=587

# 邮箱用户名
SMTP_USERNAME=<EMAIL>

# 邮箱密码或应用密码
SMTP_PASSWORD=your-email-password

# 发件人邮箱
FROM_EMAIL=<EMAIL>

# ===========================================
# 监控配置 (可选)
# ===========================================

# 启用性能监控
ENABLE_MONITORING=false

# 监控数据保留天数
MONITORING_RETENTION_DAYS=30

# ===========================================
# 缓存配置 (可选)
# ===========================================

# 启用缓存
ENABLE_CACHE=false

# 缓存类型 (memory/redis)
CACHE_TYPE=memory

# Redis连接URL (如果使用Redis缓存)
REDIS_URL=redis://localhost:6379/0

# 缓存过期时间 (秒)
CACHE_TIMEOUT=300

# ===========================================
# 备份配置
# ===========================================

# 自动备份间隔 (小时)
BACKUP_INTERVAL=24

# 备份文件保留天数
BACKUP_RETENTION_DAYS=30

# 备份压缩
BACKUP_COMPRESS=true

# ===========================================
# 开发配置 (仅开发环境)
# ===========================================

# 启用调试模式
DEBUG=false

# 启用自动重载
AUTO_RELOAD=false

# 启用详细错误信息
VERBOSE_ERRORS=false

# ===========================================
# 客户端配置 (用于客户端连接)
# ===========================================

# VPS服务器URL
VPS_URL=https://verify.yuzeguitar.me

# 客户端密钥 (与SECRET_KEY相同)
CLIENT_SECRET_KEY=your-32-character-hex-secret-key-here

# ===========================================
# 高级配置
# ===========================================

# 数据库连接池大小
DB_POOL_SIZE=10

# 数据库连接超时 (秒)
DB_TIMEOUT=30

# 静态文件缓存时间 (秒)
STATIC_CACHE_TIMEOUT=86400

# 启用GZIP压缩
ENABLE_GZIP=true

# 最大请求体大小 (MB)
MAX_CONTENT_LENGTH=16

# ===========================================
# SSL/TLS配置
# ===========================================

# SSL证书文件路径 (如果手动配置SSL)
SSL_CERT_PATH=/etc/ssl/certs/musicqr.crt

# SSL私钥文件路径
SSL_KEY_PATH=/etc/ssl/private/musicqr.key

# 强制HTTPS重定向
FORCE_HTTPS=true

# ===========================================
# 第三方服务配置 (可选)
# ===========================================

# 短信服务配置 (如果需要短信通知)
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret

# 云存储配置 (如果需要云备份)
CLOUD_STORAGE_TYPE=s3
CLOUD_STORAGE_BUCKET=your-backup-bucket
CLOUD_STORAGE_ACCESS_KEY=your-access-key
CLOUD_STORAGE_SECRET_KEY=your-secret-key

# ===========================================
# 配置说明
# ===========================================

# 1. SECRET_KEY 生成方法:
#    python3 -c "import secrets; print(secrets.token_hex(32))"
#
# 2. 强密码生成方法:
#    python3 -c "import secrets; print(secrets.token_urlsafe(16))"
#
# 3. 配置文件权限设置:
#    chmod 600 .env
#    chown musicqr:musicqr .env
#
# 4. 环境变量优先级:
#    命令行环境变量 > .env文件 > 默认值
#
# 5. 配置验证:
#    python3 -c "from config import Config; print('配置加载成功')"
