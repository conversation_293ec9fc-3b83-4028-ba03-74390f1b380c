# 乐谱验证系统 - Nginx配置示例
# 文件位置: /etc/nginx/sites-available/musicqr

# 上游服务器配置
upstream musicqr_backend {
    # 主应用服务器
    server 127.0.0.1:5000 weight=1 max_fails=3 fail_timeout=30s;
    
    # 如果有多个应用实例，可以添加更多服务器
    # server 127.0.0.1:5001 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 32;
}

# 限制请求频率
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=admin_limit:10m rate=5r/s;

# HTTPS服务器配置
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name verify.yuzeguitar.me;
    
    # SSL证书配置 (Let's Encrypt)
    ssl_certificate /etc/letsencrypt/live/verify.yuzeguitar.me/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/verify.yuzeguitar.me/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # 安全头配置
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:;" always;
    
    # 隐藏服务器版本信息
    server_tokens off;
    
    # 客户端配置
    client_max_body_size 16M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 管理后台路由 (优先级最高)
    location /admin {
        # 频率限制
        limit_req zone=admin_limit burst=10 nodelay;
        
        # 代理到后端应用
        proxy_pass http://musicqr_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓存控制
        proxy_cache_bypass $http_upgrade;
        proxy_no_cache $http_upgrade;
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 502 503 504 /50x.html;
    }
    
    # API路由
    location /api/ {
        # 频率限制
        limit_req zone=api_limit burst=20 nodelay;
        
        # 代理到后端应用
        proxy_pass http://musicqr_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # API响应缓存 (状态接口缓存5分钟)
        location /api/status {
            proxy_pass http://musicqr_backend;
            proxy_cache api_cache;
            proxy_cache_valid 200 5m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # 验证接口不缓存
        location /api/verify/ {
            proxy_pass http://musicqr_backend;
            proxy_no_cache 1;
            proxy_cache_bypass 1;
        }
    }
    
    # 静态文件服务
    location / {
        root /var/www/musicqr/web;
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
            
            # 启用gzip压缩
            gzip_static on;
        }
        
        # HTML文件缓存
        location ~* \.(html|htm)$ {
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }
        
        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 错误页面
    location = /50x.html {
        root /var/www/musicqr/web;
        internal;
    }
    
    # 日志配置
    access_log /var/log/nginx/musicqr_access.log combined;
    error_log /var/log/nginx/musicqr_error.log warn;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name verify.yuzeguitar.me;
    
    # Let's Encrypt验证
    location /.well-known/acme-challenge/ {
        root /var/www/musicqr/web;
    }
    
    # 重定向到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# 缓存配置
proxy_cache_path /var/cache/nginx/musicqr levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=60m use_temp_path=off;

# Gzip压缩配置
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;

# 日志格式
log_format main_ext '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '"$host" sn="$server_name" '
                    'rt=$request_time '
                    'ua="$upstream_addr" us="$upstream_status" '
                    'ut="$upstream_response_time" ul="$upstream_response_length" '
                    'cs=$upstream_cache_status';

# 备用配置 (如果需要多域名支持)
# server {
#     listen 443 ssl http2;
#     server_name www.verify.yuzeguitar.me;
#     
#     ssl_certificate /etc/letsencrypt/live/verify.yuzeguitar.me/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/verify.yuzeguitar.me/privkey.pem;
#     
#     return 301 https://verify.yuzeguitar.me$request_uri;
# }

# 配置说明:
# 1. 启用配置: sudo ln -s /etc/nginx/sites-available/musicqr /etc/nginx/sites-enabled/
# 2. 测试配置: sudo nginx -t
# 3. 重载配置: sudo systemctl reload nginx
# 4. 创建缓存目录: sudo mkdir -p /var/cache/nginx/musicqr && sudo chown nginx:nginx /var/cache/nginx/musicqr
# 5. SSL证书申请: sudo certbot --nginx -d verify.yuzeguitar.me
