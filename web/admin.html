<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理中心 - 乐谱验证系统</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 管理界面专用样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-item {
            background: white;
            border: 2px solid black;
            padding: 25px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .stat-item:hover {
            transform: translateY(-2px);
        }
        
        .stat-item h3 {
            margin: 0 0 15px 0;
            font-size: 1em;
            font-weight: normal;
            color: black;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: black;
            margin: 0;
            font-family: Georgia, serif;
        }
        
        .section {
            margin: 40px 0;
        }
        
        .section h3 {
            font-size: 1.4em;
            margin-bottom: 20px;
            color: black;
            border-bottom: 2px solid black;
            padding-bottom: 10px;
        }
        
        .codes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .code-item {
            background: white;
            border: 1px solid black;
            padding: 12px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .queries-list {
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid black;
            background: white;
        }
        
        .query-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        
        .query-item:last-child {
            border-bottom: none;
        }
        
        .query-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .query-time {
            color: #666;
            font-size: 0.9em;
        }
        
        .query-platform {
            color: #888;
            font-size: 0.8em;
            text-align: right;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        
        .btn-danger {
            background: black;
            color: white;
            border: 2px solid black;
        }
        
        .btn-danger:hover {
            background: white;
            color: black;
        }
        
        .note {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        
        .form-group {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        
        .input {
            padding: 12px 15px;
            border: 2px solid black;
            background: white;
            color: black;
            font-size: 1em;
            min-width: 200px;
        }
        
        .input:focus {
            outline: none;
            border-color: #333;
        }
        
        .error-message {
            color: black;
            text-align: center;
            margin-top: 10px;
            padding: 10px;
            border: 1px solid black;
            background: #f8f8f8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .query-item {
                grid-template-columns: 1fr;
                gap: 8px;
                text-align: center;
            }
            
            .query-platform {
                text-align: center;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .actions .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>管理中心</h1>
            <p class="subtitle">乐谱验证系统后台管理</p>
        </div>

        <div class="card" id="loginCard">
            <h2>管理员登录</h2>
            <div class="form-group">
                <input type="password" id="passwordInput" placeholder="请输入管理密码" class="input">
                <button onclick="login()" class="btn btn-primary">登录</button>
            </div>
            <div id="loginError" class="error-message hidden">密码错误</div>
        </div>

        <div class="card hidden" id="dashboardCard">
            <h2>系统统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <h3>有效验证码</h3>
                    <div class="stat-number" id="totalCodes">0</div>
                </div>
                <div class="stat-item">
                    <h3>查询次数</h3>
                    <div class="stat-number" id="totalQueries">0</div>
                </div>
                <div class="stat-item">
                    <h3>今日查询</h3>
                    <div class="stat-number" id="todayQueries">0</div>
                </div>
            </div>

            <div class="section">
                <h3>验证码列表</h3>
                <div class="code-list" id="codeList">
                    <!-- 验证码列表将在这里显示 -->
                </div>
            </div>

            <div class="section">
                <h3>最近查询记录</h3>
                <div class="query-list" id="queryList">
                    <!-- 查询记录将在这里显示 -->
                </div>
            </div>

            <div class="actions">
                <button onclick="exportData()" class="btn btn-secondary">导出数据</button>
                <button onclick="clearData()" class="btn btn-danger">清空记录</button>
                <button onclick="logout()" class="btn btn-secondary">退出登录</button>
            </div>
        </div>

        <div class="footer">
            <p>正版乐谱验证系统管理中心 - Yuze Pan</p>
        </div>
    </div>

    <script>
        const ADMIN_PASSWORD = 'admin123';

        function login() {
            const password = document.getElementById('passwordInput').value;
            if (password === ADMIN_PASSWORD) {
                document.getElementById('loginCard').classList.add('hidden');
                document.getElementById('dashboardCard').classList.remove('hidden');
                loadDashboard();
            } else {
                document.getElementById('loginError').classList.remove('hidden');
            }
        }

        function logout() {
            document.getElementById('loginCard').classList.remove('hidden');
            document.getElementById('dashboardCard').classList.add('hidden');
            document.getElementById('passwordInput').value = '';
            document.getElementById('loginError').classList.add('hidden');
        }

        function loadDashboard() {
            loadValidCodes();
            loadQueryStats();
            loadRecentQueries();
        }

        function loadValidCodes() {
            // 通过iframe获取验证页面的验证码列表
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'simple_verify.html';
            
            iframe.onload = function() {
                try {
                    const validCodes = iframe.contentWindow.getValidCodes();
                    displayValidCodes(validCodes);
                } catch (e) {
                    console.error('无法获取验证码列表:', e);
                    // 备用方案：显示提示信息
                    document.getElementById('codeList').innerHTML = 
                        '<p class="note">无法获取验证码列表，请检查验证页面</p>';
                }
                document.body.removeChild(iframe);
            };
            
            document.body.appendChild(iframe);
        }

        function displayValidCodes(codes) {
            document.getElementById('totalCodes').textContent = codes.length;
            
            const codeList = document.getElementById('codeList');
            if (codes.length === 0) {
                codeList.innerHTML = '<p class="note">暂无验证码</p>';
                return;
            }

            let html = '<div class="codes-grid">';
            codes.forEach(code => {
                html += `<div class="code-item">${code}</div>`;
            });
            html += '</div>';
            
            codeList.innerHTML = html;
        }

        function loadQueryStats() {
            const queries = JSON.parse(localStorage.getItem('queries') || '[]');
            
            // 总查询次数
            document.getElementById('totalQueries').textContent = queries.length;
            
            // 今日查询次数
            const today = new Date().toDateString();
            const todayQueries = queries.filter(q => 
                new Date(q.query_time).toDateString() === today
            );
            document.getElementById('todayQueries').textContent = todayQueries.length;
        }

        function loadRecentQueries() {
            const queries = JSON.parse(localStorage.getItem('queries') || '[]');
            const queryList = document.getElementById('queryList');
            
            if (queries.length === 0) {
                queryList.innerHTML = '<p class="note">暂无查询记录</p>';
                return;
            }

            // 显示最近10条记录
            const recentQueries = queries.slice(-10).reverse();
            let html = '<div class="queries-list">';
            
            recentQueries.forEach(query => {
                const queryTime = new Date(query.query_time).toLocaleString('zh-CN');
                html += `
                    <div class="query-item">
                        <div class="query-code">${query.code}</div>
                        <div class="query-time">${queryTime}</div>
                        <div class="query-platform">${query.ip_hint.platform}</div>
                    </div>
                `;
            });
            
            html += '</div>';
            queryList.innerHTML = html;
        }

        function exportData() {
            const queries = JSON.parse(localStorage.getItem('queries') || '[]');
            const data = {
                export_time: new Date().toISOString(),
                total_queries: queries.length,
                queries: queries
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `queries_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearData() {
            if (confirm('确定要清空所有查询记录吗？此操作不可恢复。')) {
                localStorage.removeItem('queries');
                loadDashboard();
                alert('查询记录已清空');
            }
        }

        // 回车键登录
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('passwordInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html> 