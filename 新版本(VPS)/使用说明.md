# 乐谱二维码验证系统 - 使用说明

## 🎵 系统简介

乐谱二维码验证系统VPS版本是一个基于云服务器的正版乐谱验证解决方案。用户扫描二维码后，系统会实时验证授权码的有效性，并记录激活状态。

### 主要特性

- ✅ **实时验证**: 基于VPS的在线验证服务
- ✅ **状态管理**: 自动记录激活时间和状态
- ✅ **安全通信**: HTTPS加密和API密钥认证
- ✅ **优雅界面**: 极简黑白设计，支持移动端
- ✅ **完整统计**: 详细的使用数据和分析

## 📱 用户使用流程

### 1. 扫描二维码

用户使用手机扫描乐谱上的二维码，会自动跳转到验证页面：
```
https://verify.yuzeguitar.me/?code=XXXXXXXXXX
```

### 2. 验证过程

1. **加载页面**: 显示"正在验证中..."
2. **连接服务器**: 向VPS发送验证请求
3. **返回结果**: 显示验证成功或失败

### 3. 验证结果

#### ✅ 验证成功
- 显示绿色勾号
- 显示"这是一本正版乐谱"
- 显示验证码和验证时间
- 如果是首次激活，会显示激活时间

#### ❌ 验证失败
- 显示红色叉号
- 显示错误原因
- 提供重新验证选项
- 支持手动输入验证码

### 4. 手动输入验证码

如果二维码损坏或扫描失败，用户可以：
1. 点击"手动输入验证码"按钮
2. 输入12位验证码
3. 点击"验证"按钮

## 💻 管理员操作指南

### 本地生成授权码

#### 1. 安装客户端

```bash
cd 新版本\(VPS\)/client
pip install -r requirements.txt
```

#### 2. 配置客户端

编辑 `config.py` 或设置环境变量：

```bash
# 设置环境变量
export VPS_URL='https://verify.yuzeguitar.me'
export CLIENT_SECRET_KEY='your-secret-key'
export API_KEY_SALT='musicqr_api_salt_2024'
```

#### 3. 生成授权码

```bash
python generate_codes.py
```

按提示操作：
1. 输入生成数量（默认50个）
2. 选择PDF格式（横版/竖版）
3. 选择是否自动同步到VPS

#### 4. 输出文件

生成完成后会得到：
- **PDF文件**: `output/qrcode_sheet_横版.pdf` 或 `output/qrcode_sheet_竖版.pdf`
- **二维码图片**: `output/qrcodes/qr_XXXXXXXXXX.png`
- **数据文件**: `data/codes.json`

### VPS服务器管理

#### 1. 查看系统状态

```bash
# 使用管理脚本
./manage.sh status

# 或直接使用systemctl
sudo systemctl status musicqr-api
sudo systemctl status nginx
```

#### 2. 查看实时日志

```bash
# API服务日志
./manage.sh logs

# Nginx访问日志
sudo tail -f /var/log/nginx/musicqr_access.log
```

#### 3. 数据库管理

```bash
# 备份数据库
./manage.sh backup

# 查看统计信息
./manage.sh stats

# 恢复数据库
./manage.sh restore
```

#### 4. 服务管理

```bash
# 重启服务
./manage.sh restart

# 停止服务
./manage.sh stop

# 启动服务
./manage.sh start
```

## 📊 数据统计和分析

### 1. 查看统计信息

```bash
# 在VPS上运行
./manage.sh stats
```

会显示：
- 总授权码数量
- 已激活数量
- 激活率
- 最近7天的激活趋势
- 系统资源使用情况

### 2. 数据库查询

```bash
# 连接数据库
sqlite3 /var/lib/musicqr/musicqr.db

# 查看所有授权码
SELECT * FROM auth_codes LIMIT 10;

# 查看激活统计
SELECT 
    DATE(activation_date) as date,
    COUNT(*) as activations
FROM auth_codes 
WHERE activated = 1 
GROUP BY DATE(activation_date)
ORDER BY date DESC;

# 查看查询频率最高的授权码
SELECT code, query_count 
FROM auth_codes 
ORDER BY query_count DESC 
LIMIT 10;
```

### 3. 导出数据

```bash
# 导出所有授权码数据
sqlite3 -header -csv /var/lib/musicqr/musicqr.db "SELECT * FROM auth_codes;" > codes_export.csv

# 导出激活统计
sqlite3 -header -csv /var/lib/musicqr/musicqr.db "
SELECT 
    code,
    created_date,
    activated,
    activation_date,
    query_count
FROM auth_codes 
ORDER BY created_date DESC;" > activation_stats.csv
```

## 🔧 高级配置

### 1. 自定义域名

如果要使用自己的域名：

1. **修改DNS记录**：
   ```
   类型: A
   名称: your-domain.com
   值: VPS-IP地址
   ```

2. **更新Nginx配置**：
   ```bash
   sudo nano /etc/nginx/sites-available/musicqr
   # 修改 server_name 为你的域名
   ```

3. **重新申请SSL证书**：
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

4. **更新客户端配置**：
   ```python
   VPS_URL = 'https://your-domain.com'
   ```

### 2. 批量操作

#### 批量同步历史数据

```python
# 在客户端运行
from generate_codes import VPSQRCodeGenerator

generator = VPSQRCodeGenerator()
success, message = generator.sync_codes_to_vps()
print(message)
```

#### 批量查询验证码状态

```bash
# 在VPS上运行
sqlite3 /var/lib/musicqr/musicqr.db << 'EOF'
.mode column
.headers on
SELECT 
    code,
    CASE WHEN activated = 1 THEN '已激活' ELSE '未激活' END as status,
    activation_date,
    query_count
FROM auth_codes 
WHERE code IN ('CODE1', 'CODE2', 'CODE3');
EOF
```

### 3. 监控和告警

#### 设置监控脚本

```bash
# 创建监控脚本
cat > /var/www/musicqr/monitor.sh << 'EOF'
#!/bin/bash
# 系统监控脚本

# 检查服务状态
if ! systemctl is-active --quiet musicqr-api; then
    echo "API服务异常" | mail -s "系统告警" <EMAIL>
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [[ $DISK_USAGE -gt 80 ]]; then
    echo "磁盘使用率过高: ${DISK_USAGE}%" | mail -s "磁盘告警" <EMAIL>
fi

# 检查数据库大小
DB_SIZE=$(du -m /var/lib/musicqr/musicqr.db | cut -f1)
if [[ $DB_SIZE -gt 100 ]]; then
    echo "数据库文件过大: ${DB_SIZE}MB" | mail -s "数据库告警" <EMAIL>
fi
EOF

chmod +x /var/www/musicqr/monitor.sh

# 设置定时监控（每小时检查一次）
(crontab -l 2>/dev/null; echo "0 * * * * /var/www/musicqr/monitor.sh") | crontab -
```

## 🚨 故障处理

### 常见问题及解决方案

#### 1. 验证码同步失败

**问题**: 本地客户端无法同步到VPS

**解决方案**:
```bash
# 检查网络连接
curl -I https://verify.yuzeguitar.me

# 检查API密钥
python3 -c "from config import ClientConfig; print(ClientConfig().API_KEY)"

# 检查VPS服务状态
./manage.sh health
```

#### 2. 用户验证失败

**问题**: 用户扫码后显示验证失败

**解决方案**:
```bash
# 检查授权码是否存在
sqlite3 /var/lib/musicqr/musicqr.db "SELECT * FROM auth_codes WHERE code='XXXXXXXXXX';"

# 检查API服务日志
sudo journalctl -u musicqr-api -n 50

# 测试API接口
curl "https://verify.yuzeguitar.me/api/verify/XXXXXXXXXX"
```

#### 3. 网站无法访问

**问题**: 用户无法打开验证页面

**解决方案**:
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查防火墙
sudo ufw status

# 检查DNS解析
nslookup verify.yuzeguitar.me

# 检查SSL证书
sudo certbot certificates
```

#### 4. 数据库损坏

**问题**: 数据库文件损坏

**解决方案**:
```bash
# 检查数据库完整性
sqlite3 /var/lib/musicqr/musicqr.db "PRAGMA integrity_check;"

# 恢复最近的备份
./manage.sh restore

# 重建数据库索引
sqlite3 /var/lib/musicqr/musicqr.db "REINDEX;"
```

## 📞 技术支持

### 联系方式

- **开发者**: Yuze Pan
- **微信**: Guitar_yuze
- **项目版本**: v2.0.0

### 获取帮助

1. **查看日志**: 首先检查相关日志文件
2. **运行诊断**: 使用 `./manage.sh health` 进行健康检查
3. **查看文档**: 参考部署指南和故障排除章节
4. **联系支持**: 如问题无法解决，请联系技术支持

### 系统更新

定期检查更新：
```bash
# 检查系统更新
sudo apt update && sudo apt list --upgradable

# 更新应用
./manage.sh update

# 备份数据
./manage.sh backup
```

---

**祝您使用愉快！** 🎵
