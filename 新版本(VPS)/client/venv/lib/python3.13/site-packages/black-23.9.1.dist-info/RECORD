../../../bin/black,sha256=2VapHcfsMelH_g1vDHya4C1JbpgMd86-7OfbpetlMeQ,268
../../../bin/blackd,sha256=kSHNFJGeH-OD1OQ452hmNMiyiXVWGWm_0kH6MOp9HBQ,269
__pycache__/_black_version.cpython-313.pyc,,
_black_version.py,sha256=O1AMkbyvLgrgRPDrm8R1vzmlGHlhNKCstYe_J3eWCV0,19
black-23.9.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
black-23.9.1.dist-info/METADATA,sha256=QUM4e4jozgMqFALCBcQsOIzOfuYL9cE6DpeJ3R-FGFg,65132
black-23.9.1.dist-info/RECORD,,
black-23.9.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-23.9.1.dist-info/WHEEL,sha256=9QBuHhg6FNW7lppboF2vKVbCGTVzsFykgRQjjlajrhA,87
black-23.9.1.dist-info/entry_points.txt,sha256=qBIyywHwGRkJj7kieq86kqf77rz3qGC4Joj36lHnxwc,78
black-23.9.1.dist-info/licenses/AUTHORS.md,sha256=8drxTtCp41j9z9NFJ9U37R1m9qL0zwTMELvgHFFkwao,8092
black-23.9.1.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.py,sha256=4pcsz7Rv8dgN59oE4zvRRBEMUnBwK7nGq9xh-LqMl-Y,45778
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/__pycache__/__init__.cpython-313.pyc,,
black/__pycache__/__main__.cpython-313.pyc,,
black/__pycache__/_width_table.cpython-313.pyc,,
black/__pycache__/brackets.cpython-313.pyc,,
black/__pycache__/cache.cpython-313.pyc,,
black/__pycache__/comments.cpython-313.pyc,,
black/__pycache__/concurrency.cpython-313.pyc,,
black/__pycache__/const.cpython-313.pyc,,
black/__pycache__/debug.cpython-313.pyc,,
black/__pycache__/files.cpython-313.pyc,,
black/__pycache__/handle_ipynb_magics.cpython-313.pyc,,
black/__pycache__/linegen.cpython-313.pyc,,
black/__pycache__/lines.cpython-313.pyc,,
black/__pycache__/mode.cpython-313.pyc,,
black/__pycache__/nodes.cpython-313.pyc,,
black/__pycache__/numerics.cpython-313.pyc,,
black/__pycache__/output.cpython-313.pyc,,
black/__pycache__/parsing.cpython-313.pyc,,
black/__pycache__/report.cpython-313.pyc,,
black/__pycache__/rusty.cpython-313.pyc,,
black/__pycache__/strings.cpython-313.pyc,,
black/__pycache__/trans.cpython-313.pyc,,
black/_width_table.py,sha256=2lSnE4s_nVXXfIj9hP2qWASqX8I003WxBM5xPnelDrQ,10761
black/brackets.py,sha256=v8ffcCsfPZymOrx1lvHkK09-_sCh-jBfEjruO7wlMJ4,12163
black/cache.py,sha256=v5XA1M9beM_hjYYSljvqaC_8c3zSTfUBCAgKI9rwU54,4559
black/comments.py,sha256=3016YMQp_TOn5R00MgZmvTxWGEW4IKdxr1tkhNnb014,12681
black/concurrency.py,sha256=YsjgBNlASAvup7pSg_WPeRR53plJsmL7LlQfb_XL1So,6281
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=PDBJeevHiMPzdQTZmC4kCZbQxyBXOSzQvIVnrFCXALU,1594
black/files.py,sha256=ixL_Cfifpokkp3tLB--qW-X2s09puR-kjynDFQrqbjc,13925
black/handle_ipynb_magics.py,sha256=pw_WWBMxY9MsFdjzDI2knW1r8WsBQvwuPndf_awh1mM,13466
black/linegen.py,sha256=R2HjH2ATGnAN3BhoKIYME5yoJ6HHorZouCAi4s5LwDg,61494
black/lines.py,sha256=srZm37kjgCLBvhqkwkA_0Mu2SNXrGzMH1JBuQeyYrTU,38155
black/mode.py,sha256=ECWJtSO0Px1lyEJ80GDc_OT6lqPXLyJ33m3KYGiAI5Y,7924
black/nodes.py,sha256=zi1GmErX4xLLuKxAajPzmV8Ax4tzqK9ZrTjbdikDTHo,26668
black/numerics.py,sha256=TBID6blEBXZgGIigMXS5JpX2-Trv9lK3WoWPkkHrpac,1653
black/output.py,sha256=qfdOuT8z5WSm_GxwP24X5XrjrxPacjj1k1gC40crJGw,3486
black/parsing.py,sha256=2iRQyXbkpYVEXZk2_WFlPDzCfsdY6VIxIaK-WCirUn0,7862
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/report.py,sha256=vcZXcQwoT2LPYD0AoL7w3tshKw_i5DjxGRsqqLZQksc,3451
black/rusty.py,sha256=kmKIJpD9J0bPfkQ67Sy8HkVcx-5CF02acggiaaxM7sc,556
black/strings.py,sha256=Ra4sHKZWmNGLA7QIYH4e6EApGn40Bt-7ipbwsbMoGz8,11098
black/trans.py,sha256=GuUQdVUC9PgEAcaf0UoM2pRZZ8q4n0YMyxka6vkatz4,91262
blackd/__init__.py,sha256=i_Hf0Q6R8TpsZ_5MVA36D9CXW8ecFTNNM-Hn46179pE,8148
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/__pycache__/__init__.cpython-313.pyc,,
blackd/__pycache__/__main__.cpython-313.pyc,,
blackd/__pycache__/middlewares.cpython-313.pyc,,
blackd/middlewares.py,sha256=QS7cs86Ojuaqh64dGneimhJ-f30rDI646c27ts4Dwh0,1585
blib2to3/Grammar.txt,sha256=HZFjxB15qLjf6Hh1ebaAtC15-XlTF6p5dLEg7TRO0IA,11530
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/__pycache__/__init__.cpython-313.pyc,,
blib2to3/__pycache__/pygram.cpython-313.pyc,,
blib2to3/__pycache__/pytree.cpython-313.pyc,,
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/__pycache__/__init__.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/conv.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/driver.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/grammar.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/literals.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/parse.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/pgen.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/token.cpython-313.pyc,,
blib2to3/pgen2/__pycache__/tokenize.cpython-313.pyc,,
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.py,sha256=ppu5nhL9iL2eUuWR0pzOxV3FFqM04F3rt-Ckwf5tz_I,10565
blib2to3/pgen2/grammar.py,sha256=WQBX_vZFq8RNVNPX49J8oNdwXeWhXhizUu5vPwD0ZVM,6858
blib2to3/pgen2/literals.py,sha256=_LyRryELzqarFkW3OAEZzZ-yppCTm9g0mjqqQ2XygKE,1614
blib2to3/pgen2/parse.py,sha256=JpI0y5x9OSO7hIbLOpjcARSVBeQNm50QlgQkiHtRVqc,14831
blib2to3/pgen2/pgen.py,sha256=iQH8W999TKUT5AhuOpW38ZynwSACkVNV-I6z8kyQozY,15428
blib2to3/pgen2/token.py,sha256=iT30kH8_qqhvxuzyUpiIiO3SGxuxqopZBBg-s1x8Vzo,1805
blib2to3/pgen2/tokenize.py,sha256=YoQZYgETSjn2WCc3N0fL67vq2oakg85K4MLfQ_NhFcI,23111
blib2to3/pygram.py,sha256=O_adyJWbBE8Bd8VWfY5NMa1kcBw_fYiel_OCBL9PkBs,5813
blib2to3/pytree.py,sha256=dedSbfx56FTkyTOA1A-I4eTVyDuZ0VRZ_eq0H5HmgLc,32569
