# 乐谱二维码验证系统 - VPS部署指南

## 🎯 部署概述

本指南将帮助你在Ubuntu 22.04 VPS上部署乐谱二维码验证系统。整个过程大约需要30-60分钟。

### 系统要求

- **操作系统**: Ubuntu 22.04 LTS
- **内存**: 最低1GB，推荐2GB
- **存储**: 最低10GB可用空间
- **网络**: 公网IP地址
- **域名**: verify.yuzeguitar.me (或你的自定义域名)

## 📋 部署前准备

### 1. VPS基础配置

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 创建应用用户
sudo adduser musicqr
sudo usermod -aG sudo musicqr

# 切换到应用用户
su - musicqr
```

### 2. 上传代码

将整个 `新版本(VPS)` 目录上传到VPS的 `/home/<USER>/` 目录下：

```bash
# 方法1: 使用scp
scp -r 新版本\(VPS\)/ musicqr@your-vps-ip:/home/<USER>/

# 方法2: 使用git (推荐)
git clone https://github.com/your-repo/musicqr-vps.git
cd musicqr-vps
```

### 3. 域名DNS配置

在你的域名管理面板中添加A记录：
```
类型: A
名称: verify.yuzeguitar.me
值: 你的VPS公网IP
TTL: 300
```

## 🚀 一键部署

### 运行部署脚本

```bash
cd /home/<USER>/新版本\(VPS\)/server
chmod +x deploy/setup.sh
./deploy/setup.sh
```

部署脚本将自动完成：
- ✅ 安装系统依赖
- ✅ 配置Python环境
- ✅ 部署应用代码
- ✅ 配置数据库
- ✅ 设置systemd服务
- ✅ 配置Nginx
- ✅ 设置防火墙
- ✅ 启动所有服务

### 配置SSL证书

部署完成后，配置HTTPS：

```bash
sudo certbot --nginx -d verify.yuzeguitar.me
```

## 🔧 手动部署步骤

如果自动部署失败，可以按以下步骤手动部署：

### 1. 安装依赖

```bash
sudo apt install -y python3 python3-pip python3-venv nginx sqlite3 git curl
```

### 2. 创建目录结构

```bash
sudo mkdir -p /var/www/musicqr
sudo mkdir -p /var/lib/musicqr
sudo mkdir -p /var/log/musicqr
sudo mkdir -p /var/backups/musicqr

sudo chown -R musicqr:musicqr /var/www/musicqr
sudo chown -R musicqr:musicqr /var/lib/musicqr
sudo chown -R musicqr:musicqr /var/log/musicqr
sudo chown -R musicqr:musicqr /var/backups/musicqr
```

### 3. 部署应用

```bash
# 复制代码
cp -r server/* /var/www/musicqr/
cp -r web/ /var/www/musicqr/

# 设置Python环境
cd /var/www/musicqr
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 初始化数据库
python3 -c "from models import init_db; init_db('/var/lib/musicqr/musicqr.db')"
```

### 4. 配置环境变量

```bash
cat > /var/www/musicqr/.env << EOF
FLASK_ENV=production
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")
API_KEY_SALT=musicqr_api_salt_2024
DATABASE_PATH=/var/lib/musicqr/musicqr.db
LOG_FILE=/var/log/musicqr/api.log
BACKUP_DIR=/var/backups/musicqr
EOF

chmod 600 /var/www/musicqr/.env
```

### 5. 配置systemd服务

```bash
sudo cp deploy/systemd.service /etc/systemd/system/musicqr-api.service
sudo systemctl daemon-reload
sudo systemctl enable musicqr-api
sudo systemctl start musicqr-api
```

### 6. 配置Nginx

```bash
sudo cp deploy/nginx.conf /etc/nginx/sites-available/musicqr
sudo ln -s /etc/nginx/sites-available/musicqr /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl restart nginx
```

### 7. 配置防火墙

```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw --force enable
```

## ✅ 部署验证

### 1. 检查服务状态

```bash
# 检查API服务
sudo systemctl status musicqr-api

# 检查Nginx服务
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep -E ':(80|443|5000)'
```

### 2. 测试API接口

```bash
# 测试本地API
curl http://localhost:5000/api/status

# 测试外网访问
curl http://verify.yuzeguitar.me/api/status
```

### 3. 测试前端页面

在浏览器中访问：
- HTTP: `http://verify.yuzeguitar.me`
- HTTPS: `https://verify.yuzeguitar.me` (配置SSL后)

## 🔑 配置客户端

### 1. 获取API密钥

```bash
cd /var/www/musicqr
source venv/bin/activate
python3 -c "from config import generate_api_key; print('API密钥:', generate_api_key())"
```

### 2. 配置本地客户端

在本地客户端的 `config.py` 中配置：

```python
# 生产环境配置
VPS_URL = 'https://verify.yuzeguitar.me'
SECRET_KEY = 'your-secret-key-from-server'
API_KEY_SALT = 'musicqr_api_salt_2024'
```

或使用环境变量：

```bash
export CLIENT_ENV='production'
export VPS_URL='https://verify.yuzeguitar.me'
export CLIENT_SECRET_KEY='your-secret-key-from-server'
export API_KEY_SALT='musicqr_api_salt_2024'
```

## 📊 系统管理

### 使用管理脚本

```bash
# 复制管理脚本
cp deploy/管理脚本.sh /var/www/musicqr/manage.sh
chmod +x /var/www/musicqr/manage.sh

# 查看帮助
./manage.sh help

# 常用命令
./manage.sh status    # 查看状态
./manage.sh logs      # 查看日志
./manage.sh backup    # 备份数据库
./manage.sh stats     # 查看统计
./manage.sh health    # 健康检查
```

### 日常维护命令

```bash
# 重启服务
sudo systemctl restart musicqr-api

# 查看日志
sudo journalctl -u musicqr-api -f

# 备份数据库
./manage.sh backup

# 更新应用
./manage.sh update
```

## 🔒 安全配置

### 1. 防火墙规则

```bash
# 查看当前规则
sudo ufw status

# 只允许必要端口
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
```

### 2. SSL证书自动续期

```bash
# 测试续期
sudo certbot renew --dry-run

# 查看定时任务
crontab -l
```

### 3. 定期备份

```bash
# 设置每日备份
(crontab -l 2>/dev/null; echo "0 2 * * * /var/www/musicqr/manage.sh backup") | crontab -
```

## 🐛 故障排除

### 常见问题

1. **API服务启动失败**
   ```bash
   sudo journalctl -u musicqr-api -n 50
   ```

2. **Nginx配置错误**
   ```bash
   sudo nginx -t
   sudo tail -f /var/log/nginx/error.log
   ```

3. **数据库权限问题**
   ```bash
   sudo chown musicqr:musicqr /var/lib/musicqr/musicqr.db
   ```

4. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :5000
   sudo kill -9 PID
   ```

### 日志位置

- **API日志**: `/var/log/musicqr/api.log`
- **Nginx访问日志**: `/var/log/nginx/musicqr_access.log`
- **Nginx错误日志**: `/var/log/nginx/musicqr_error.log`
- **系统日志**: `sudo journalctl -u musicqr-api`

## 📈 性能优化

### 1. 数据库优化

```bash
# 定期清理查询日志
sqlite3 /var/lib/musicqr/musicqr.db "DELETE FROM query_logs WHERE query_time < DATE('now', '-30 days');"

# 数据库真空清理
sqlite3 /var/lib/musicqr/musicqr.db "VACUUM;"
```

### 2. 日志轮转

```bash
# 配置logrotate
sudo tee /etc/logrotate.d/musicqr << EOF
/var/log/musicqr/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 musicqr musicqr
    postrotate
        systemctl reload musicqr-api
    endscript
}
EOF
```

## 🎉 部署完成

恭喜！你已经成功部署了乐谱二维码验证系统。

### 下一步

1. **测试完整流程**：
   - 本地生成验证码
   - 同步到VPS
   - 扫码验证

2. **监控系统**：
   - 设置监控告警
   - 定期检查日志
   - 监控资源使用

3. **备份策略**：
   - 定期备份数据库
   - 备份配置文件
   - 测试恢复流程

### 技术支持

如有问题，请联系：
- **开发者**: Yuze Pan
- **微信**: Guitar_yuze
- **项目版本**: v2.0.0
