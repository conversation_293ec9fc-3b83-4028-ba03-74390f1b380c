# 乐谱验证系统 Nginx 配置文件
# 文件位置: /etc/nginx/sites-available/musicqr

server {
    listen 80;
    server_name verify.yuzeguitar.me;
    
    # 重定向到HTTPS (SSL配置后启用)
    # return 301 https://$server_name$request_uri;
    
    # 根目录指向前端文件
    root /var/www/musicqr/web;
    index index.html;
    
    # 主页面路由
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
    
    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # 启用gzip压缩
        gzip_static on;
    }
    
    # API代理到Flask应用
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 502 503 504 /50x.html;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|db)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 限制请求大小
    client_max_body_size 1M;
    
    # 日志配置
    access_log /var/log/nginx/musicqr_access.log combined;
    error_log /var/log/nginx/musicqr_error.log warn;
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/musicqr/web;
        internal;
    }
    
    location = /50x.html {
        root /var/www/musicqr/web;
        internal;
    }
}

# HTTPS配置 (SSL证书配置后自动生成)
# server {
#     listen 443 ssl http2;
#     server_name verify.yuzeguitar.me;
#     
#     # SSL证书配置
#     ssl_certificate /etc/letsencrypt/live/verify.yuzeguitar.me/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/verify.yuzeguitar.me/privkey.pem;
#     
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # 其他配置与HTTP相同...
# }

# 限流配置
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;

# 在server块中使用限流
# location /api/ {
#     limit_req zone=api burst=20 nodelay;
#     # ... 其他配置
# }

# Gzip压缩配置
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;
