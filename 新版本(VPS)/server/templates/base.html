<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}乐谱验证系统 - 管理后台{% endblock %}</title>
    <style>
        /* 管理后台样式 - 极简黑白设计 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', 'Times', serif;
            background: #ffffff;
            color: #000000;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* 导航栏 */
        .navbar {
            background: #000000;
            color: #ffffff;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #000000;
        }

        .navbar h1 {
            font-size: 1.5em;
            font-weight: 300;
        }

        .navbar .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar a {
            color: #ffffff;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 1px solid #ffffff;
            transition: all 0.2s ease;
        }

        .navbar a:hover {
            background: #ffffff;
            color: #000000;
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* 卡片样式 */
        .card {
            background: #ffffff;
            border: 2px solid #000000;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .card h2 {
            font-size: 1.8em;
            margin-bottom: 1rem;
            font-weight: 300;
            border-bottom: 1px solid #000000;
            padding-bottom: 0.5rem;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border: 1px solid #000000;
        }

        th {
            background: #000000;
            color: #ffffff;
            font-weight: 500;
        }

        tr:nth-child(even) {
            background: #f8f8f8;
        }

        tr:hover {
            background: #f0f0f0;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border: 2px solid #000000;
            background: #ffffff;
            color: #000000;
            text-decoration: none;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0.25rem;
        }

        .btn:hover {
            background: #000000;
            color: #ffffff;
        }

        .btn-primary {
            background: #000000;
            color: #ffffff;
        }

        .btn-primary:hover {
            background: #333333;
            border-color: #333333;
        }

        .btn-danger {
            border-color: #cc0000;
            color: #cc0000;
        }

        .btn-danger:hover {
            background: #cc0000;
            color: #ffffff;
        }

        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9em;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #000000;
            background: #ffffff;
            color: #000000;
            font-size: 1em;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #333333;
            background: #f8f8f8;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #ffffff;
            border: 2px solid #000000;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 300;
            color: #000000;
            display: block;
        }

        .stat-label {
            color: #666666;
            margin-top: 0.5rem;
        }

        /* 搜索栏 */
        .search-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            align-items: end;
        }

        .search-bar .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #000000;
            text-decoration: none;
            color: #000000;
        }

        .pagination a:hover {
            background: #000000;
            color: #ffffff;
        }

        .pagination .current {
            background: #000000;
            color: #ffffff;
        }

        /* 消息提示 */
        .flash-messages {
            margin-bottom: 2rem;
        }

        .flash-message {
            padding: 1rem;
            border: 2px solid;
            margin-bottom: 1rem;
        }

        .flash-success {
            border-color: #007700;
            background: #f0fff0;
            color: #007700;
        }

        .flash-error {
            border-color: #cc0000;
            background: #fff0f0;
            color: #cc0000;
        }

        .flash-info {
            border-color: #0066cc;
            background: #f0f8ff;
            color: #0066cc;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .navbar {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .search-bar {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            table {
                font-size: 0.9em;
            }

            th, td {
                padding: 0.5rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.admin_logged_in %}
    <nav class="navbar">
        <h1>🎵 乐谱验证系统 - 管理后台</h1>
        <div class="user-info">
            <span>欢迎，{{ session.admin_username }}</span>
            <a href="{{ url_for('admin_dashboard') }}">仪表板</a>
            <a href="{{ url_for('admin_codes') }}">授权码管理</a>
            <a href="{{ url_for('admin_logout') }}">退出</a>
        </div>
    </nav>
    {% endif %}

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message flash-{{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
