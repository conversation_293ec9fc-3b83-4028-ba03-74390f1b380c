#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乐谱二维码验证系统 - VPS后端API
功能：授权码管理、验证服务、状态跟踪
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import sqlite3
import hashlib
import hmac
import json
from datetime import datetime
import os
import logging
from typing import Dict, List, Optional, Tuple
import ipaddress

# 导入配置和模型
from config import Config
from models import init_db, AuthCode

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('musicqr_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)  # 允许跨域请求

# 初始化数据库
init_db()

class AuthCodeManager:
    """授权码管理器"""
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
    
    def get_db_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def verify_api_key(self, api_key: str) -> bool:
        """验证API密钥"""
        if not api_key:
            return False
        
        # 使用HMAC验证API密钥
        expected = hmac.new(
            Config.SECRET_KEY.encode(),
            Config.API_KEY_SALT.encode(),
            hashlib.sha256
        ).hexdigest()
        
        provided = hmac.new(
            Config.SECRET_KEY.encode(),
            api_key.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(expected, provided)
    
    def sync_codes(self, codes_data: List[Dict], api_key: str) -> Tuple[bool, str, Dict]:
        """
        同步授权码到数据库
        
        Args:
            codes_data: 授权码数据列表
            api_key: API密钥
            
        Returns:
            Tuple[bool, str, Dict]: (成功状态, 消息, 统计信息)
        """
        # 验证API密钥
        if not self.verify_api_key(api_key):
            return False, "API密钥无效", {}
        
        if not codes_data:
            return False, "没有提供授权码数据", {}
        
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        added_count = 0
        skipped_count = 0
        error_count = 0
        
        try:
            for code_info in codes_data:
                code = code_info.get('code', '').strip().upper()
                created_date = code_info.get('created_date')
                
                if not code or len(code) != 12:
                    error_count += 1
                    continue
                
                # 检查是否已存在
                cursor.execute("SELECT id FROM auth_codes WHERE code = ?", (code,))
                if cursor.fetchone():
                    skipped_count += 1
                    continue
                
                # 插入新授权码
                cursor.execute("""
                    INSERT INTO auth_codes (code, created_date, activated, query_count)
                    VALUES (?, ?, FALSE, 0)
                """, (code, created_date or datetime.now().isoformat()))
                
                added_count += 1
            
            conn.commit()
            
            stats = {
                'added': added_count,
                'skipped': skipped_count,
                'errors': error_count,
                'total': len(codes_data)
            }
            
            logger.info(f"同步授权码完成: {stats}")
            return True, f"成功同步 {added_count} 个授权码", stats
            
        except Exception as e:
            conn.rollback()
            logger.error(f"同步授权码失败: {e}")
            return False, f"数据库错误: {str(e)}", {}
        
        finally:
            conn.close()
    
    def verify_code(self, code: str, client_ip: str = None, user_agent: str = None) -> Tuple[bool, Dict]:
        """
        验证授权码
        
        Args:
            code: 授权码
            client_ip: 客户端IP
            user_agent: 用户代理
            
        Returns:
            Tuple[bool, Dict]: (验证结果, 详细信息)
        """
        if not code or len(code.strip()) != 12:
            return False, {
                'valid': False,
                'message': '授权码格式无效',
                'activated': False
            }
        
        code = code.strip().upper()
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 查询授权码
            cursor.execute("""
                SELECT id, code, activated, activation_date, query_count
                FROM auth_codes WHERE code = ?
            """, (code,))
            
            result = cursor.fetchone()
            
            if not result:
                return False, {
                    'valid': False,
                    'message': '授权码不存在或无效',
                    'activated': False
                }
            
            # 更新查询统计
            new_query_count = result['query_count'] + 1
            now = datetime.now().isoformat()
            
            # 如果是首次激活
            if not result['activated']:
                cursor.execute("""
                    UPDATE auth_codes 
                    SET activated = TRUE, 
                        activation_date = ?, 
                        activation_ip = ?, 
                        activation_user_agent = ?,
                        query_count = ?,
                        last_query_date = ?
                    WHERE id = ?
                """, (now, client_ip, user_agent, new_query_count, now, result['id']))
                
                conn.commit()
                
                logger.info(f"授权码首次激活: {code} from {client_ip}")
                
                return True, {
                    'valid': True,
                    'activated': True,
                    'activation_date': now,
                    'message': '验证成功！这是正版乐谱',
                    'first_activation': True
                }
            else:
                # 更新查询记录
                cursor.execute("""
                    UPDATE auth_codes 
                    SET query_count = ?, last_query_date = ?
                    WHERE id = ?
                """, (new_query_count, now, result['id']))
                
                conn.commit()
                
                return True, {
                    'valid': True,
                    'activated': True,
                    'activation_date': result['activation_date'],
                    'message': '验证成功！这是正版乐谱',
                    'first_activation': False
                }
                
        except Exception as e:
            logger.error(f"验证授权码失败: {e}")
            return False, {
                'valid': False,
                'message': '服务器错误，请稍后重试',
                'activated': False
            }
        
        finally:
            conn.close()
    
    def get_stats(self) -> Dict:
        """获取系统统计信息"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        
        try:
            # 总授权码数量
            cursor.execute("SELECT COUNT(*) as total FROM auth_codes")
            total = cursor.fetchone()['total']
            
            # 已激活数量
            cursor.execute("SELECT COUNT(*) as activated FROM auth_codes WHERE activated = TRUE")
            activated = cursor.fetchone()['activated']
            
            # 今日查询数量
            today = datetime.now().date().isoformat()
            cursor.execute("""
                SELECT COUNT(*) as today_queries 
                FROM auth_codes 
                WHERE DATE(last_query_date) = ?
            """, (today,))
            today_queries = cursor.fetchone()['today_queries']
            
            return {
                'total_codes': total,
                'activated_codes': activated,
                'activation_rate': round(activated / total * 100, 2) if total > 0 else 0,
                'today_queries': today_queries
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
        
        finally:
            conn.close()

# 创建授权码管理器实例
auth_manager = AuthCodeManager()

def get_client_ip() -> str:
    """获取客户端真实IP"""
    # 检查代理头
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

@app.route('/')
def index():
    """首页 - 显示系统状态"""
    stats = auth_manager.get_stats()
    
    html_template = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>乐谱验证系统 - API服务</title>
        <style>
            body { font-family: Georgia, serif; margin: 40px; background: #f8f8f8; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border: 1px solid #ddd; }
            h1 { color: #333; text-align: center; margin-bottom: 30px; }
            .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0; }
            .stat-item { text-align: center; padding: 20px; background: #f8f8f8; border: 1px solid #eee; }
            .stat-number { font-size: 2em; font-weight: bold; color: #333; }
            .stat-label { color: #666; margin-top: 5px; }
            .api-info { margin-top: 30px; padding: 20px; background: #f0f0f0; border-left: 4px solid #333; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎵 乐谱验证系统</h1>
            <p style="text-align: center; color: #666;">API服务运行中</p>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">{{ stats.total_codes or 0 }}</div>
                    <div class="stat-label">总授权码</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats.activated_codes or 0 }}</div>
                    <div class="stat-label">已激活</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats.activation_rate or 0 }}%</div>
                    <div class="stat-label">激活率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats.today_queries or 0 }}</div>
                    <div class="stat-label">今日查询</div>
                </div>
            </div>
            
            <div class="api-info">
                <h3>API端点</h3>
                <p><strong>验证授权码:</strong> GET /api/verify/{code}</p>
                <p><strong>同步授权码:</strong> POST /api/sync-codes</p>
                <p><strong>系统状态:</strong> GET /api/status</p>
            </div>
            
            <div class="footer">
                <p>乐谱验证系统 v2.0 - Yuze Pan</p>
                <p>服务时间: {{ current_time }}</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    return render_template_string(html_template, 
                                stats=stats, 
                                current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

@app.route('/api/verify/<code>')
def verify_code_api(code):
    """验证授权码API"""
    client_ip = get_client_ip()
    user_agent = request.headers.get('User-Agent', '')
    
    success, result = auth_manager.verify_code(code, client_ip, user_agent)
    
    if success:
        return jsonify(result), 200
    else:
        return jsonify(result), 400

@app.route('/api/sync-codes', methods=['POST'])
def sync_codes_api():
    """同步授权码API"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '无效的请求数据'}), 400
        
        codes_data = data.get('codes', [])
        api_key = data.get('api_key', '')
        
        success, message, stats = auth_manager.sync_codes(codes_data, api_key)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'stats': stats
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"同步API错误: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@app.route('/api/status')
def status_api():
    """系统状态API"""
    stats = auth_manager.get_stats()
    return jsonify({
        'status': 'running',
        'timestamp': datetime.now().isoformat(),
        'stats': stats
    })

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"服务器内部错误: {error}")
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    # 开发环境运行
    app.run(host='0.0.0.0', port=5000, debug=False)
