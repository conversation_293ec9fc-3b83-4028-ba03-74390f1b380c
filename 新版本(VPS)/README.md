# 乐谱二维码验证系统 - VPS版本

## 项目概述

这是一个基于VPS的乐谱二维码验证系统，用于验证乐谱的正版性。系统包含三个主要组件：

1. **本地Python客户端** - 生成授权码和PDF，同步到VPS
2. **VPS后端API** - 管理授权码状态和验证服务
3. **前端验证页面** - 用户扫码验证界面

## 系统架构

```
本地环境                    VPS服务器 (verify.yuzeguitar.me)
┌─────────────────┐        ┌─────────────────────────────────┐
│ Python客户端     │   HTTP  │ Flask后端API                    │
│ generate_codes.py│ ────→  │ - 授权码管理                     │
│ - 生成授权码     │        │ - SQLite数据库                   │
│ - 生成PDF       │        │ - 状态管理                       │
│ - 同步到VPS     │        │                                │
└─────────────────┘        │ Nginx + 前端页面                │
                           │ - 验证界面                       │
用户扫码访问                │ - API通信                       │
┌─────────────────┐        │                                │
│ 手机/浏览器      │   HTTPS │ verify.yuzeguitar.me/           │
│ 扫描二维码       │ ────→  │ ?code=XXXXXXXXXX                │
└─────────────────┘        └─────────────────────────────────┘
```

## 工作流程

### 1. 授权码生成和同步
1. 运行本地Python客户端生成授权码
2. 生成包含二维码的PDF文件
3. 自动将授权码同步到VPS数据库（状态：未激活）

### 2. 用户验证流程
1. 用户扫描二维码访问：`verify.yuzeguitar.me/?code=XXXXXXXXXX`
2. 前端页面向VPS API发送验证请求
3. VPS检查授权码有效性并返回结果
4. 如果是首次验证正版码，更新状态为"已激活"并记录激活时间

## 技术栈

### 后端 (VPS)
- **Python 3.9+**
- **Flask** - Web框架
- **SQLite** - 数据库
- **Nginx** - 反向代理和静态文件服务
- **Ubuntu 22.04** - 操作系统

### 前端
- **HTML5 + CSS3 + JavaScript**
- **响应式设计** - 支持移动端
- **极简黑白设计风格** - 保持原有美学

### 本地客户端
- **Python 3.9+**
- **reportlab** - PDF生成
- **qrcode** - 二维码生成
- **requests** - HTTP通信

## 目录结构

```
新版本(VPS)/
├── README.md                 # 项目说明
├── server/                   # VPS服务器端代码
│   ├── app.py               # Flask应用主文件
│   ├── models.py            # 数据库模型
│   ├── config.py            # 配置文件
│   ├── requirements.txt     # Python依赖
│   └── deploy/              # 部署脚本
│       ├── setup.sh         # VPS环境配置脚本
│       ├── nginx.conf       # Nginx配置
│       └── systemd.service  # 系统服务配置
├── client/                   # 本地客户端
│   ├── generate_codes.py    # 优化版代码生成器
│   ├── config.py            # 客户端配置
│   └── requirements.txt     # Python依赖
└── web/                      # 前端页面
    ├── index.html           # 验证页面
    ├── style.css            # 样式文件
    └── script.js            # JavaScript逻辑
```

## 数据库设计

### 授权码表 (auth_codes)
```sql
CREATE TABLE auth_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(12) UNIQUE NOT NULL,           -- 授权码
    created_date DATETIME NOT NULL,             -- 创建时间
    activated BOOLEAN DEFAULT FALSE,            -- 是否已激活
    activation_date DATETIME NULL,              -- 激活时间
    activation_ip VARCHAR(45) NULL,             -- 激活IP
    activation_user_agent TEXT NULL,            -- 激活设备信息
    query_count INTEGER DEFAULT 0,             -- 查询次数
    last_query_date DATETIME NULL              -- 最后查询时间
);
```

## API接口

### 1. 同步授权码 (POST /api/sync-codes)
**用途**: 本地客户端同步授权码到VPS

**请求体**:
```json
{
    "codes": [
        {
            "code": "XXXXXXXXXX",
            "created_date": "2024-01-01T12:00:00"
        }
    ],
    "api_key": "your-api-key"
}
```

### 2. 验证授权码 (GET /api/verify/{code})
**用途**: 前端验证授权码有效性

**响应**:
```json
{
    "valid": true,
    "activated": false,
    "activation_date": null,
    "message": "验证成功"
}
```

## 部署说明

### VPS要求
- Ubuntu 22.04 LTS
- 2GB RAM (最低1GB)
- 20GB 存储空间
- 域名: verify.yuzeguitar.me

### 快速部署
1. 上传代码到VPS
2. 运行部署脚本: `bash deploy/setup.sh`
3. 配置域名DNS解析
4. 配置SSL证书

## 安全特性

1. **API密钥认证** - 本地客户端同步需要密钥
2. **HTTPS加密** - 所有通信使用SSL
3. **IP记录** - 记录激活IP防止滥用
4. **查询限制** - 防止恶意查询
5. **数据库备份** - 定期备份授权码数据

## 使用方法

### 本地生成授权码
```bash
cd client/
python generate_codes.py
```

### VPS服务管理
```bash
# 启动服务
sudo systemctl start musicqr-api

# 查看状态
sudo systemctl status musicqr-api

# 查看日志
sudo journalctl -u musicqr-api -f
```

## 更新日志

### v2.0.0 (VPS版本)
- ✅ 基于VPS的分布式架构
- ✅ 实时授权码验证
- ✅ 状态管理和激活记录
- ✅ 安全的API通信
- ✅ 优雅的前端界面
- ✅ 完整的部署方案

---

**开发者**: Yuze Pan  
**联系方式**: vx:Guitar_yuze  
**项目版本**: v2.0.0
