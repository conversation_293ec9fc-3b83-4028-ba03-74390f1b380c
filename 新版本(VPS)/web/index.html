<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乐谱验证系统 - 正版验证</title>
    <link rel="stylesheet" href="style.css">
    <meta name="description" content="乐谱正版验证系统，扫码验证乐谱真伪">
    <meta name="keywords" content="乐谱,验证,正版,二维码,Guitar_yuze">
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://verify.yuzeguitar.me">
    <link rel="dns-prefetch" href="https://verify.yuzeguitar.me">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🎵 乐谱验证系统</h1>
            <p class="subtitle">正版乐谱真伪验证</p>
        </div>

        <!-- 加载状态卡片 -->
        <div class="card" id="loadingCard">
            <div class="loading">
                <div class="spinner"></div>
                <p>正在验证中...</p>
                <p class="loading-detail" id="loadingDetail">连接服务器...</p>
            </div>
        </div>

        <!-- 验证成功卡片 -->
        <div class="card hidden" id="validCard">
            <div class="success-icon">✓</div>
            <h2>验证成功</h2>
            <p>这是一本<strong>正版乐谱</strong></p>
            <div class="book-info">
                <p><strong>验证码：</strong><span id="codeDisplay"></span></p>
                <p><strong>验证时间：</strong><span id="verifyTime"></span></p>
                <p><strong>激活状态：</strong><span id="activationStatus"></span></p>
                <div id="activationInfo" class="activation-info hidden">
                    <p><strong>首次激活时间：</strong><span id="activationTime"></span></p>
                </div>
                <p class="note">感谢您购买正版乐谱</p>
            </div>
        </div>

        <!-- 验证失败卡片 -->
        <div class="card hidden" id="invalidCard">
            <div class="error-icon">×</div>
            <h2>验证失败</h2>
            <p id="errorMessage">此验证码无效或不存在</p>
            <div class="error-details">
                <p>可能的原因：</p>
                <ul>
                    <li>验证码不正确</li>
                    <li>二维码已损坏</li>
                    <li>非正版乐谱</li>
                    <li>网络连接问题</li>
                </ul>
            </div>
            <div class="action-buttons">
                <button class="btn btn-secondary" onclick="location.reload()">重新验证</button>
                <button class="btn btn-primary" onclick="showManualInput()">手动输入验证码</button>
            </div>
        </div>

        <!-- 手动输入卡片 -->
        <div class="card hidden" id="manualInputCard">
            <div class="info-icon">ℹ</div>
            <h2>手动输入验证码</h2>
            <p>请输入12位验证码</p>
            <div class="input-group">
                <input type="text" id="manualCodeInput" placeholder="请输入验证码" maxlength="12" 
                       style="text-transform: uppercase; letter-spacing: 2px; text-align: center;">
                <button class="btn btn-primary" onclick="verifyManualCode()">验证</button>
            </div>
            <p class="note">验证码通常位于二维码下方</p>
            <button class="btn btn-secondary" onclick="showMainCards()">返回</button>
        </div>

        <!-- 网络错误卡片 -->
        <div class="card hidden" id="networkErrorCard">
            <div class="error-icon">⚠</div>
            <h2>网络连接错误</h2>
            <p>无法连接到验证服务器</p>
            <div class="error-details">
                <p>请检查：</p>
                <ul>
                    <li>网络连接是否正常</li>
                    <li>是否能访问互联网</li>
                    <li>稍后重试</li>
                </ul>
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="location.reload()">重新连接</button>
                <button class="btn btn-secondary" onclick="showManualInput()">手动输入</button>
            </div>
        </div>

        <!-- 页面底部 -->
        <div class="footer">
            <p>正版乐谱验证系统 v2.0 - Yuze Pan</p>
            <p><small>如有问题，请联系 vx:Guitar_yuze</small></p>
            <p class="tech-info"><small>Powered by VPS API</small></p>
        </div>
    </div>

    <!-- 加载JavaScript -->
    <script src="script.js"></script>
    
    <!-- 错误处理 -->
    <script>
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            // 如果JavaScript加载失败，显示基本错误信息
            if (e.filename && e.filename.includes('script.js')) {
                document.getElementById('loadingCard').innerHTML = `
                    <div class="error-icon">×</div>
                    <h2>加载错误</h2>
                    <p>页面资源加载失败，请刷新重试</p>
                    <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
                `;
            }
        });
        
        // 页面加载超时处理
        setTimeout(function() {
            if (!window.BookVerifier) {
                document.getElementById('loadingDetail').textContent = '加载超时，请刷新页面';
                setTimeout(function() {
                    if (!window.BookVerifier) {
                        location.reload();
                    }
                }, 3000);
            }
        }, 10000);
    </script>
</body>
</html>
