#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乐谱二维码验证系统 - 测试脚本
用于测试系统各个组件的功能
"""

import requests
import json
import sqlite3
import os
import sys
import time
from datetime import datetime
import hashlib
import hmac

class SystemTester:
    """系统测试类"""
    
    def __init__(self, vps_url="http://localhost:5000", api_key=None):
        self.vps_url = vps_url.rstrip('/')
        self.api_key = api_key or self.generate_test_api_key()
        self.test_codes = []
        self.results = []
    
    def generate_test_api_key(self):
        """生成测试API密钥"""
        secret_key = "test-secret-key-2024"
        salt = "musicqr_api_salt_2024"
        return hmac.new(
            secret_key.encode(),
            salt.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def log_result(self, test_name, success, message="", details=None):
        """记录测试结果"""
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if details and not success:
            print(f"   详情: {details}")
    
    def test_vps_connection(self):
        """测试VPS连接"""
        print("\n🔍 测试VPS连接...")
        
        try:
            response = requests.get(f"{self.vps_url}/", timeout=10)
            if response.status_code == 200:
                self.log_result("VPS连接", True, f"连接成功 ({response.status_code})")
            else:
                self.log_result("VPS连接", False, f"HTTP状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            self.log_result("VPS连接", False, "无法连接到服务器", self.vps_url)
        except requests.exceptions.Timeout:
            self.log_result("VPS连接", False, "连接超时")
        except Exception as e:
            self.log_result("VPS连接", False, "连接异常", str(e))
    
    def test_api_status(self):
        """测试API状态接口"""
        print("\n🔍 测试API状态接口...")
        
        try:
            response = requests.get(f"{self.vps_url}/api/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'status' in data and data['status'] == 'running':
                    self.log_result("API状态", True, "API服务正常运行")
                else:
                    self.log_result("API状态", False, "API状态异常", data)
            else:
                self.log_result("API状态", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_result("API状态", False, "API状态检查失败", str(e))
    
    def test_sync_codes(self):
        """测试授权码同步"""
        print("\n🔍 测试授权码同步...")
        
        # 生成测试授权码
        test_codes = [
            {
                "code": "TEST12345678",
                "created_date": datetime.now().isoformat()
            },
            {
                "code": "TEST87654321",
                "created_date": datetime.now().isoformat()
            }
        ]
        
        self.test_codes = [code["code"] for code in test_codes]
        
        sync_data = {
            "codes": test_codes,
            "api_key": self.api_key
        }
        
        try:
            response = requests.post(
                f"{self.vps_url}/api/sync-codes",
                json=sync_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    stats = result.get('stats', {})
                    self.log_result("授权码同步", True, 
                                  f"同步成功，添加: {stats.get('added', 0)}")
                else:
                    self.log_result("授权码同步", False, "同步失败", result)
            else:
                error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                self.log_result("授权码同步", False, f"HTTP {response.status_code}", error_data)
                
        except Exception as e:
            self.log_result("授权码同步", False, "同步请求异常", str(e))
    
    def test_verify_codes(self):
        """测试授权码验证"""
        print("\n🔍 测试授权码验证...")
        
        if not self.test_codes:
            self.log_result("授权码验证", False, "没有测试授权码")
            return
        
        for code in self.test_codes:
            try:
                response = requests.get(f"{self.vps_url}/api/verify/{code}", timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('valid'):
                        self.log_result(f"验证-{code}", True, "验证成功")
                    else:
                        self.log_result(f"验证-{code}", False, "验证失败", result.get('message'))
                else:
                    self.log_result(f"验证-{code}", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"验证-{code}", False, "验证请求异常", str(e))
    
    def test_invalid_code(self):
        """测试无效授权码"""
        print("\n🔍 测试无效授权码...")
        
        invalid_code = "INVALID12345"
        
        try:
            response = requests.get(f"{self.vps_url}/api/verify/{invalid_code}", timeout=10)
            
            if response.status_code == 400:
                result = response.json()
                if not result.get('valid'):
                    self.log_result("无效授权码", True, "正确识别无效授权码")
                else:
                    self.log_result("无效授权码", False, "错误地验证了无效授权码")
            else:
                self.log_result("无效授权码", False, f"意外的HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_result("无效授权码", False, "测试异常", str(e))
    
    def test_api_security(self):
        """测试API安全性"""
        print("\n🔍 测试API安全性...")
        
        # 测试无效API密钥
        invalid_sync_data = {
            "codes": [{"code": "SECURITY123", "created_date": datetime.now().isoformat()}],
            "api_key": "invalid-api-key"
        }
        
        try:
            response = requests.post(
                f"{self.vps_url}/api/sync-codes",
                json=invalid_sync_data,
                timeout=10
            )
            
            if response.status_code == 400:
                result = response.json()
                if not result.get('success'):
                    self.log_result("API安全", True, "正确拒绝无效API密钥")
                else:
                    self.log_result("API安全", False, "错误地接受了无效API密钥")
            else:
                self.log_result("API安全", False, f"意外的HTTP状态码: {response.status_code}")
                
        except Exception as e:
            self.log_result("API安全", False, "安全测试异常", str(e))
    
    def test_frontend_access(self):
        """测试前端页面访问"""
        print("\n🔍 测试前端页面...")
        
        # 测试主页
        try:
            response = requests.get(f"{self.vps_url}/", timeout=10)
            if response.status_code == 200 and 'html' in response.headers.get('content-type', ''):
                self.log_result("前端主页", True, "主页访问正常")
            else:
                self.log_result("前端主页", False, f"HTTP {response.status_code}")
        except Exception as e:
            self.log_result("前端主页", False, "主页访问异常", str(e))
        
        # 测试带参数的验证页面
        if self.test_codes:
            test_code = self.test_codes[0]
            try:
                response = requests.get(f"{self.vps_url}/?code={test_code}", timeout=10)
                if response.status_code == 200:
                    self.log_result("验证页面", True, "验证页面访问正常")
                else:
                    self.log_result("验证页面", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_result("验证页面", False, "验证页面访问异常", str(e))
    
    def test_database_operations(self):
        """测试数据库操作（仅在本地运行时）"""
        print("\n🔍 测试数据库操作...")
        
        db_path = "/var/lib/musicqr/musicqr.db"
        if not os.path.exists(db_path):
            db_path = "musicqr.db"  # 本地测试数据库
        
        if not os.path.exists(db_path):
            self.log_result("数据库", False, "数据库文件不存在")
            return
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 测试查询
            cursor.execute("SELECT COUNT(*) FROM auth_codes")
            count = cursor.fetchone()[0]
            self.log_result("数据库查询", True, f"数据库包含 {count} 个授权码")
            
            # 测试插入（如果是测试数据库）
            if "test" in db_path.lower() or db_path == "musicqr.db":
                test_code = f"DBTEST{int(time.time())}"
                cursor.execute("""
                    INSERT OR IGNORE INTO auth_codes (code, created_date, activated, query_count)
                    VALUES (?, ?, FALSE, 0)
                """, (test_code, datetime.now().isoformat()))
                
                if cursor.rowcount > 0:
                    self.log_result("数据库插入", True, "测试数据插入成功")
                else:
                    self.log_result("数据库插入", True, "数据已存在（正常）")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.log_result("数据库", False, "数据库操作异常", str(e))
    
    def test_performance(self):
        """测试系统性能"""
        print("\n🔍 测试系统性能...")
        
        if not self.test_codes:
            self.log_result("性能测试", False, "没有测试授权码")
            return
        
        test_code = self.test_codes[0]
        response_times = []
        
        # 进行10次验证请求
        for i in range(10):
            try:
                start_time = time.time()
                response = requests.get(f"{self.vps_url}/api/verify/{test_code}", timeout=10)
                end_time = time.time()
                
                if response.status_code == 200:
                    response_times.append(end_time - start_time)
                
            except Exception:
                pass
        
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            if avg_time < 1.0:  # 平均响应时间小于1秒
                self.log_result("性能测试", True, 
                              f"平均响应时间: {avg_time:.3f}s (最小: {min_time:.3f}s, 最大: {max_time:.3f}s)")
            else:
                self.log_result("性能测试", False, 
                              f"响应时间过长: {avg_time:.3f}s")
        else:
            self.log_result("性能测试", False, "无法获取响应时间数据")
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        # 这里可以添加清理测试数据的逻辑
        # 例如删除测试授权码等
        pass
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 测试报告")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
                    if result['details']:
                        print(f"    详情: {result['details']}")
        
        # 保存详细报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total': total_tests,
                    'passed': passed_tests,
                    'failed': failed_tests,
                    'success_rate': passed_tests/total_tests*100
                },
                'results': self.results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return failed_tests == 0
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始系统测试...")
        print(f"🎯 目标服务器: {self.vps_url}")
        print(f"🔑 API密钥: {self.api_key[:8]}...")
        
        # 执行所有测试
        self.test_vps_connection()
        self.test_api_status()
        self.test_sync_codes()
        self.test_verify_codes()
        self.test_invalid_code()
        self.test_api_security()
        self.test_frontend_access()
        self.test_database_operations()
        self.test_performance()
        
        # 清理测试数据
        self.cleanup_test_data()
        
        # 生成报告
        success = self.generate_report()
        
        if success:
            print("\n🎉 所有测试通过！系统运行正常。")
        else:
            print("\n⚠️ 部分测试失败，请检查系统配置。")
        
        return success

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='乐谱二维码验证系统测试脚本')
    parser.add_argument('--url', default='http://localhost:5000', 
                       help='VPS服务器地址 (默认: http://localhost:5000)')
    parser.add_argument('--api-key', help='API密钥')
    parser.add_argument('--production', action='store_true', 
                       help='生产环境测试 (使用 https://verify.yuzeguitar.me)')
    
    args = parser.parse_args()
    
    # 确定测试URL
    if args.production:
        vps_url = 'https://verify.yuzeguitar.me'
    else:
        vps_url = args.url
    
    # 创建测试器
    tester = SystemTester(vps_url=vps_url, api_key=args.api_key)
    
    # 运行测试
    success = tester.run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
