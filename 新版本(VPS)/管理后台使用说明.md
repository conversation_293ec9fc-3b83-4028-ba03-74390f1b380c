# 乐谱验证系统 - 管理后台使用说明

## 🎯 管理后台概述

管理后台是一个基于Web的管理界面，让你可以通过浏览器轻松管理所有授权码，查看系统统计，监控运行状态。

### 🔗 访问地址
```
https://verify.yuzeguitar.me/admin
```

### 🔐 默认登录信息
- **用户名**: `admin`
- **密码**: `musicqr2024`

## 📋 功能详解

### 1. 🏠 仪表板 (`/admin/dashboard`)

**主要功能**：
- 📊 **系统统计**: 总授权码数、已激活数、激活率、今日查询数
- 📈 **7天激活趋势**: 可视化图表显示最近一周的激活情况
- 📝 **最近激活**: 显示最新激活的10个授权码
- ⚙️ **系统状态**: 服务运行状态、数据库大小、服务器时间
- 🚀 **快速操作**: 一键跳转到各个管理功能

**使用场景**：
- 每日查看系统运行概况
- 监控授权码使用趋势
- 快速了解系统健康状态

### 2. 🔑 授权码管理 (`/admin/codes`)

**主要功能**：
- 📋 **列表显示**: 分页显示所有授权码，支持20个/页
- 🔍 **搜索功能**: 按授权码搜索，支持模糊匹配
- 🎯 **状态筛选**: 筛选已激活/未激活的授权码
- 📊 **排序功能**: 按创建时间、激活时间、查询次数排序
- ✅ **批量操作**: 批量删除、批量导出选中的授权码
- 📄 **详情查看**: 点击详情查看单个授权码的完整信息

**操作步骤**：
1. 进入授权码管理页面
2. 使用搜索框查找特定授权码
3. 使用筛选器按状态分类
4. 勾选需要操作的授权码
5. 选择批量操作并执行

### 3. ➕ 添加授权码 (`/admin/codes/add`)

**三种添加方式**：

#### 单个添加
- 手动输入12位授权码
- 或留空自动生成
- 点击"生成随机码"按钮快速生成

#### 批量生成
- 设置生成数量（1-1000个）
- 系统自动生成唯一的授权码
- 避免与现有授权码重复

#### 导入功能
- **文本导入**: 每行一个授权码
- **CSV导入**: 格式为"授权码,创建时间"
- **文件导入**: 支持.txt和.csv文件
- **重复处理**: 可选择跳过重复的授权码

**使用示例**：
```
# 纯文本格式
ABCD12345678
EFGH87654321
IJKL13579246

# CSV格式
ABCD12345678,2024-01-01T12:00:00
EFGH87654321,2024-01-02T12:00:00
```

### 4. 🔍 授权码详情 (`/admin/codes/<code>`)

**详细信息**：
- 📋 **基本信息**: 授权码、状态、创建时间、查询次数
- 🎯 **激活信息**: 激活时间、激活IP、设备信息
- 📱 **二维码预览**: 实时生成的验证二维码
- 📝 **操作记录**: 创建、激活、查询的时间线
- 📊 **统计信息**: 查询次数、激活间隔、存在天数

**快速操作**：
- 🧪 **测试验证**: 直接测试授权码验证功能
- 📋 **复制授权码**: 一键复制到剪贴板
- 🔗 **复制验证URL**: 复制完整的验证链接
- 📊 **导出数据**: 导出该授权码的详细信息

### 5. 📊 数据导出 (`/admin/export`)

**导出功能**：
- 📄 **CSV格式**: 标准的逗号分隔值文件
- 🎯 **选择性导出**: 可导出选中的授权码
- 📅 **时间戳文件名**: 自动生成带时间戳的文件名
- 📋 **完整信息**: 包含所有字段的详细数据

**导出字段**：
- 授权码
- 创建时间
- 是否激活
- 激活时间
- 激活IP
- 查询次数
- 最后查询时间

### 6. ⚙️ 系统信息 (`/admin/system-info`)

**监控内容**：
- 🚀 **服务状态**: API服务、数据库状态
- 💻 **系统资源**: CPU、内存、磁盘使用情况
- 🗄️ **数据库信息**: 文件路径、大小、状态
- 📱 **应用信息**: 版本、API端点、安全设置
- 📝 **日志信息**: 各类日志文件位置
- 🔧 **维护操作**: 系统检查、数据库测试

**维护功能**：
- 检查API状态
- 测试数据库连接
- 查看系统日志
- 备份数据

## 🔧 管理员配置

### 修改管理员账号

1. **编辑配置文件**：
   ```bash
   sudo nano /var/www/musicqr/.env
   ```

2. **添加配置**：
   ```bash
   ADMIN_USERNAME=your_admin_name
   ADMIN_PASSWORD=your_secure_password
   ```

3. **重启服务**：
   ```bash
   sudo systemctl restart musicqr-api
   ```

### 安全建议

- 🔒 **强密码**: 使用复杂的管理员密码
- 🌐 **HTTPS**: 确保使用HTTPS访问管理后台
- 📍 **IP限制**: 考虑限制管理后台的访问IP
- 🔄 **定期更换**: 定期更换管理员密码
- 📝 **访问日志**: 定期检查访问日志

## 🚀 部署和更新

### 首次部署管理后台

```bash
# 在VPS上运行
cd ~/新版本\(VPS\)/server
bash deploy/update_admin.sh
```

### 更新管理后台

```bash
# 更新代码
cd ~/新版本\(VPS\)
git pull  # 如果使用git

# 运行更新脚本
bash server/deploy/update_admin.sh
```

## 📱 移动端支持

管理后台完全支持移动设备：
- 📱 **响应式设计**: 自适应手机和平板屏幕
- 👆 **触摸友好**: 优化的触摸操作体验
- 🔍 **移动搜索**: 移动端优化的搜索界面
- 📊 **图表适配**: 移动端友好的统计图表

## 🐛 故障排除

### 常见问题

1. **无法访问管理后台**
   ```bash
   # 检查服务状态
   sudo systemctl status musicqr-api
   sudo systemctl status nginx
   
   # 检查端口
   sudo netstat -tlnp | grep :80
   ```

2. **登录失败**
   ```bash
   # 检查配置文件
   cat /var/www/musicqr/.env | grep ADMIN
   
   # 重置为默认密码
   sudo sed -i '/ADMIN_/d' /var/www/musicqr/.env
   sudo systemctl restart musicqr-api
   ```

3. **页面显示异常**
   ```bash
   # 检查模板文件
   ls -la /var/www/musicqr/templates/
   
   # 重新复制模板
   cp -r server/templates/* /var/www/musicqr/templates/
   ```

4. **系统信息显示Unknown**
   ```bash
   # 安装psutil
   cd /var/www/musicqr
   source venv/bin/activate
   pip install psutil
   sudo systemctl restart musicqr-api
   ```

### 日志查看

```bash
# API服务日志
sudo journalctl -u musicqr-api -f

# Nginx访问日志
sudo tail -f /var/log/nginx/musicqr_access.log

# 应用日志
sudo tail -f /var/log/musicqr/api.log
```

## 📞 技术支持

如有问题，请联系：
- **开发者**: Yuze Pan
- **微信**: Guitar_yuze
- **版本**: v2.0.0

---

**祝您使用愉快！** 🎵
