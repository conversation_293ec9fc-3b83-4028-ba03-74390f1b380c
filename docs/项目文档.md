# 乐谱二维码验证系统 - 完整文档

## 📖 项目概述

这是一个基于GitHub Pages的免费二维码验证系统，专门用于乐谱书籍的真伪验证。系统采用纯静态网站架构，无需服务器费用，支持国内用户访问。

### 🎯 核心功能
- ✅ **二维码生成**：批量生成唯一验证码和可打印PDF
- ✅ **真伪验证**：用户扫码即可验证乐谱真伪
- ✅ **统计管理**：密码保护的管理后台，查看验证统计
- ✅ **自动部署**：一键生成并自动推送到GitHub Pages

### 💡 技术特点
- **完全免费**：基于GitHub Pages，无服务器费用
- **国内可访问**：静态网站，访问稳定
- **自动化**：一键生成、更新、部署
- **响应式设计**：支持手机、平板、电脑访问

---

## 🏗️ 系统架构

### 文件结构
```
乐谱二维码工程/
├── 📁 web/                    # 网站文件（会自动推送到GitHub）
│   ├── simple_verify.html     # 验证页面（主要功能）
│   ├── admin.html             # 管理后台
│   ├── index.html             # 首页（备用）
│   └── style.css              # 样式文件
├── 📁 fonts/                  # 字体文件
│   └── Bodoni 72.ttc          # PDF标题字体
├── 📁 data/                   # 数据存储
│   └── codes.json             # 验证码数据
├── 📁 output/                 # 生成文件
│   ├── qrcode_sheet.pdf       # 可打印PDF
│   └── qrcodes/               # 二维码图片
├── 📁 docs/                   # 文档目录
├── 📁 scripts/                # 脚本工具
└── generate_codes.py          # 核心生成器
```

### 技术架构
```
用户扫码 → GitHub Pages → 验证页面 → 本地存储 → 管理后台
    ↑                                              ↓
  PDF打印 ← Python生成器 ← 验证码数据 ← 统计数据
```

---

## 🎨 设计风格

### 视觉设计
- **配色方案**：纯黑白极简风格
- **字体选择**：Georgia衬线字体，优雅易读
- **布局风格**：卡片式设计，清晰层次
- **响应式**：完美适配各种设备

### PDF设计
- **格式**：横版A4，每页一个二维码
- **标题字体**：Bodoni 72（优雅现代）
- **验证码字体**：PTSerif（清晰易读）
- **标题文本**：`SCAN TO VERIFY AUTHENTICITY.`

---

## 🚀 使用方法

### 1. 生成验证码
```bash
python generate_codes.py
```
- 输入生成数量（默认50个）
- 自动生成PDF、更新网站、推送到GitHub

### 2. 访问网站
- **验证页面**：https://jerrypanguo.github.io/musicqrcode_verify/simple_verify.html
- **管理后台**：https://jerrypanguo.github.io/musicqrcode_verify/admin.html
- **管理密码**：`admin123`

### 3. 印刷流程
1. 使用生成的 `output/qrcode_sheet.pdf` 进行印刷
2. 将二维码贴在乐谱上或印在乐谱内页
3. 用户扫码即可验证真伪

---

## 💻 核心组件详解

### 1. generate_codes.py - 核心生成器

#### 主要功能
- **验证码生成**：使用安全随机算法生成12位唯一验证码
- **二维码创建**：生成指向验证页面的二维码图片
- **PDF制作**：创建横版A4格式的可打印PDF
- **网页更新**：自动更新验证页面中的验证码数组
- **自动部署**：推送web文件夹到GitHub仓库

#### 关键方法
```python
# 生成验证码
def generate_unique_code(self, length: int = 12) -> str

# 创建二维码
def create_qrcode(self, code: str) -> str

# 生成PDF
def create_pdf(self, codes_data: List[Dict])

# 自动部署
def auto_deploy_web(self) -> bool

# 批量生成（主要接口）
def generate_batch(self, count: int = 50)
```

### 2. simple_verify.html - 验证页面

#### 核心功能
- **验证码检查**：检查扫码传入的验证码是否有效
- **结果显示**：显示验证成功/失败页面
- **统计记录**：记录查询信息到localStorage
- **响应式设计**：适配各种设备

#### JavaScript核心
```javascript
// 有效验证码数组（自动更新）
const VALID_CODES = ['CODE1', 'CODE2', ...];

// 验证逻辑
class SimpleBookVerifier {
    verifyCode(code) {
        if (this.isValidCode(code)) {
            this.showValid(code);
            this.recordQuery(code);
        } else {
            this.showInvalid();
        }
    }
}
```

### 3. admin.html - 管理后台

#### 主要功能
- **登录保护**：密码验证（admin123）
- **统计显示**：有效验证码数、查询次数、今日查询
- **验证码列表**：网格显示所有有效验证码
- **查询记录**：显示最近10条查询记录
- **数据管理**：导出数据、清空记录

#### 数据通信
```javascript
// 从验证页面获取验证码列表
function loadValidCodes() {
    const iframe = document.createElement('iframe');
    iframe.src = 'simple_verify.html';
    iframe.onload = function() {
        const validCodes = iframe.contentWindow.getValidCodes();
        displayValidCodes(validCodes);
    };
}
```

### 4. style.css - 样式系统

#### 设计原则
- **极简主义**：黑白配色，无多余装饰
- **可读性**：Georgia字体，合适的行高和间距
- **一致性**：统一的按钮、卡片、表单样式
- **响应式**：媒体查询适配不同屏幕

#### 关键样式
```css
/* 主色调 */
body { background: #ffffff; color: #000000; }

/* 卡片设计 */
.card { border: 1px solid #000000; padding: 40px 30px; }

/* 按钮样式 */
.btn { border: 2px solid #000000; transition: all 0.2s ease; }

/* 响应式 */
@media (max-width: 768px) { ... }
```

---

## 🔧 配置说明

### GitHub仓库设置
1. **仓库地址**：https://github.com/jerrypanguo/musicqrcode_verify
2. **Pages设置**：启用GitHub Pages，源分支选择main
3. **访问地址**：https://jerrypanguo.github.io/musicqrcode_verify/

### 字体配置
- **Bodoni 72**：位于 `fonts/Bodoni 72.ttc`，用于PDF标题
- **PTSerif**：系统字体，用于PDF验证码
- **备选字体**：自动降级到系统可用字体

### 数据存储
- **验证码数据**：`data/codes.json`（本地存储）
- **查询统计**：浏览器localStorage（用户端存储）
- **管理数据**：通过iframe通信获取

---

## 📊 数据流程

### 生成流程
```
1. 运行generate_codes.py
2. 生成唯一验证码
3. 创建二维码图片
4. 制作PDF文件
5. 更新验证页面代码
6. 推送到GitHub仓库
7. 网站自动更新
```

### 验证流程
```
1. 用户扫描二维码
2. 跳转到验证页面
3. 解析URL中的验证码
4. 检查验证码有效性
5. 显示验证结果
6. 记录查询统计
```

### 管理流程
```
1. 访问管理后台
2. 输入密码登录
3. 获取验证码列表
4. 显示统计数据
5. 查看查询记录
6. 导出/清空数据
```

---

## 🛠️ 开发指南

### 本地开发
1. **环境要求**：Python 3.7+
2. **依赖安装**：`pip install -r requirements.txt`
3. **生成测试**：`python generate_codes.py`
4. **本地预览**：打开 `web/simple_verify.html`

### 自定义修改
1. **修改样式**：编辑 `web/style.css`
2. **修改页面**：编辑 `web/simple_verify.html`
3. **修改PDF**：编辑 `generate_codes.py` 中的PDF生成部分
4. **修改字体**：替换 `fonts/` 目录中的字体文件

### 部署流程
1. **自动部署**：运行 `generate_codes.py` 自动推送
2. **手动部署**：复制 `web/` 文件到GitHub仓库
3. **验证部署**：访问GitHub Pages地址确认更新

---

## 🔒 安全考虑

### 验证码安全
- **唯一性**：使用secrets模块生成加密安全的随机码
- **防重复**：自动检查避免重复生成
- **长度设计**：12位字符，排除易混淆字符

### 数据安全
- **本地存储**：敏感数据存储在本地，不上传云端
- **访问控制**：管理后台密码保护
- **隐私保护**：只收集必要的验证统计信息

### 系统安全
- **静态网站**：无服务器端代码，减少攻击面
- **HTTPS访问**：GitHub Pages强制HTTPS
- **版本控制**：Git记录所有变更历史

---

## 📈 性能优化

### 加载优化
- **资源压缩**：CSS/JS代码简洁高效
- **图片优化**：二维码PNG格式，大小适中
- **缓存策略**：浏览器缓存静态资源

### 用户体验
- **响应式设计**：适配所有设备
- **加载动画**：1秒延迟显示结果，提升体验感
- **错误处理**：友好的错误提示和重试机制

### 扩展性
- **模块化设计**：功能分离，易于维护
- **配置化**：URL、密码等可配置
- **批量处理**：支持大量验证码生成

---

## 🚨 故障排除

### 常见问题

#### 1. 字体加载失败
**现象**：PDF中字体显示为默认字体
**解决**：
- 检查 `fonts/Bodoni 72.ttc` 是否存在
- 确认字体文件权限正确
- 查看控制台是否有字体加载错误

#### 2. GitHub推送失败
**现象**：自动部署时Git操作失败
**解决**：
- 检查Git配置：`git config --global user.name/email`
- 确认GitHub仓库权限
- 检查网络连接

#### 3. 验证页面更新失败
**现象**：新生成的验证码无法验证
**解决**：
- 检查 `web/simple_verify.html` 中的VALID_CODES数组
- 确认 `update_verify_page.py` 执行成功
- 手动检查验证码是否添加到数组中

#### 4. 管理后台无法获取验证码
**现象**：管理后台显示"无法获取验证码列表"
**解决**：
- 检查iframe是否能正常加载验证页面
- 确认验证页面中的getValidCodes函数存在
- 检查浏览器控制台错误信息

### 调试方法
1. **查看控制台**：浏览器F12查看错误信息
2. **检查文件**：确认所有必要文件存在
3. **测试功能**：逐步测试各个功能模块
4. **查看日志**：Python脚本运行时的输出信息

---

## 📝 更新日志

### v2.0 (当前版本)
- ✅ 重构管理界面CSS，现代化设计
- ✅ 标题字体改用Bodoni 72
- ✅ 去除激活功能，专注真伪验证
- ✅ 优化自动部署流程
- ✅ 完善响应式设计

### v1.0
- ✅ 基础二维码生成功能
- ✅ 验证页面和管理后台
- ✅ PDF生成和自动部署
- ✅ GitHub Pages集成

---

## 🎯 未来规划

### 功能增强
- 📊 更详细的统计分析
- 🔍 验证码搜索功能
- 📱 移动端优化
- 🌐 多语言支持

### 技术升级
- ⚡ 性能优化
- 🔒 安全增强
- 📦 组件化重构
- 🚀 CI/CD自动化

---

## 👥 联系方式

- **开发者**：Yuze Pan
- **微信**：Guitar_yuze
- **GitHub**：https://github.com/jerrypanguo/musicqrcode_verify

---

*最后更新：2025年6月21日* 