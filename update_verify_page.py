#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新验证页面工具
功能：将生成的验证码数据自动更新到验证页面中
"""

import json
import os
import re
from typing import List


class VerifyPageUpdater:
    """验证页面更新器"""
    
    def __init__(self):
        self.data_dir = "data"
        self.web_dir = "web"
        self.codes_file = os.path.join(self.data_dir, "codes.json")
        self.verify_page = os.path.join(self.web_dir, "simple_verify.html")
    
    def load_codes(self) -> List[str]:
        """
        加载验证码数据
        
        Returns:
            List[str]: 验证码列表
        """
        if not os.path.exists(self.codes_file):
            print(f"❌ 验证码数据文件不存在: {self.codes_file}")
            return []
        
        try:
            with open(self.codes_file, 'r', encoding='utf-8') as f:
                codes_data = json.load(f)
            
            # 提取验证码字符串
            codes = [item['code'] for item in codes_data if isinstance(item, dict) and 'code' in item]
            print(f"✅ 加载了 {len(codes)} 个验证码")
            return codes
            
        except Exception as e:
            print(f"❌ 加载验证码数据失败: {e}")
            return []
    
    def update_verify_page(self, codes: List[str]) -> bool:
        """
        更新验证页面中的验证码数组
        
        Args:
            codes: 验证码列表
            
        Returns:
            bool: 更新是否成功
        """
        if not os.path.exists(self.verify_page):
            print(f"❌ 验证页面文件不存在: {self.verify_page}")
            return False
        
        try:
            # 读取页面内容
            with open(self.verify_page, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 生成新的验证码数组
            codes_array = self.generate_codes_array(codes)
            
            # 更精确的正则表达式匹配
            pattern = r'const VALID_CODES = \[\s*(?:[^[\]]*|\[[^\]]*\])*\s*\];'
            replacement = f'const VALID_CODES = {codes_array};'
            
            new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            # 检查是否成功替换
            if new_content == content:
                print("❌ 未找到要替换的验证码数组")
                print("尝试手动替换...")
                # 尝试另一种匹配方式
                pattern2 = r'const VALID_CODES = \[.*?\];'
                new_content = re.sub(pattern2, replacement, content, flags=re.DOTALL)
                
                if new_content == content:
                    print("❌ 仍然无法找到验证码数组")
                    return False
            
            # 写回文件
            with open(self.verify_page, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 验证页面已更新，包含 {len(codes)} 个验证码")
            return True
            
        except Exception as e:
            print(f"❌ 更新验证页面失败: {e}")
            return False
    
    def generate_codes_array(self, codes: List[str]) -> str:
        """
        生成JavaScript验证码数组字符串
        
        Args:
            codes: 验证码列表
            
        Returns:
            str: JavaScript数组字符串
        """
        if not codes:
            return "[\n            // 验证码将通过Python脚本自动生成和更新\n        ]"
        
        # 将验证码转换为JavaScript数组格式
        codes_str = ",\n            ".join(f"'{code}'" for code in codes)
        
        return f"""[
            {codes_str}
        ]"""
    
    def run(self):
        """运行更新过程"""
        print("=== 验证页面更新工具 ===")
        
        # 加载验证码数据
        codes = self.load_codes()
        
        if not codes:
            print("❌ 没有找到验证码数据，请先运行 generate_codes.py 生成验证码")
            return False
        
        # 更新验证页面
        success = self.update_verify_page(codes)
        
        if success:
            print("🎉 验证页面更新完成！")
            print(f"📄 验证页面: {self.verify_page}")
            print("📋 接下来的步骤：")
            print("1. 将更新后的 simple_verify.html 上传到 GitHub")
            print("2. 用户就可以通过扫描二维码进行验证了")
        else:
            print("❌ 验证页面更新失败")
        
        return success


def main():
    """主函数"""
    updater = VerifyPageUpdater()
    updater.run()


if __name__ == "__main__":
    main() 